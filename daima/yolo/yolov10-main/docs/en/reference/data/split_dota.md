---
description: Detailed guide on using YOLO with DOTA dataset for object detection, including dataset preparation, image splitting, and label handling.
keywords: Ultralytics, YOLO, DOTA dataset, object detection, image processing, python, dataset preparation, image splitting, label handling, YOLO with DOTA, computer vision, AI, machine learning
---

# Reference for `ultralytics/data/split_dota.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/split_dota.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/split_dota.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/split_dota.py) 🛠️. Thank you 🙏!

<br><br>

## ::: ultralytics.data.split_dota.bbox_iof

<br><br>

## ::: ultralytics.data.split_dota.load_yolo_dota

<br><br>

## ::: ultralytics.data.split_dota.get_windows

<br><br>

## ::: ultralytics.data.split_dota.get_window_obj

<br><br>

## ::: ultralytics.data.split_dota.crop_and_save

<br><br>

## ::: ultralytics.data.split_dota.split_images_and_labels

<br><br>

## ::: ultralytics.data.split_dota.split_trainval

<br><br>

## ::: ultralytics.data.split_dota.split_test

<br><br>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于标注数据的SERIAL数字识别测试程序
使用您标注的数据来识别ceshi文件夹中的SERIAL数字
"""

import cv2
import numpy as np
import os
import json
import re
from datetime import datetime
import pytesseract

class SerialRecognizer:
    def __init__(self):
        self.load_training_data()
        
    def load_training_data(self):
        """加载标注数据分析特征"""
        annotation_file = "serial_number_annotations.json"
        if not os.path.exists(annotation_file):
            print("❌ 未找到标注数据文件")
            return
        
        with open(annotation_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        print(f"✅ 已加载 {len(self.annotations)} 张图片的标注数据")
        
        # 分析标注数据的特征
        self.analyze_annotations()
    
    def analyze_annotations(self):
        """分析标注数据的特征"""
        print("\n📊 标注数据分析:")
        print("=" * 50)
        
        sizes = []
        positions = []
        serials = []
        
        for filename, boxes in self.annotations.items():
            print(f"📄 {filename}:")
            for box in boxes:
                x, y, w, h = box['x'], box['y'], box['w'], box['h']
                serial = box['serial']
                
                sizes.append((w, h))
                positions.append((x, y))
                serials.append(serial)
                
                print(f"   🎯 {serial} - 位置:({x},{y}) 大小:{w}x{h}")
        
        # 计算平均尺寸
        avg_w = sum(s[0] for s in sizes) / len(sizes)
        avg_h = sum(s[1] for s in sizes) / len(sizes)
        
        print(f"\n📏 平均尺寸: {avg_w:.0f} x {avg_h:.0f}")
        print(f"📍 标注数量: {len(serials)}")
        print(f"🔤 SERIAL格式: {serials}")
        
        # 保存特征用于识别
        self.avg_size = (avg_w, avg_h)
        self.known_serials = serials
    
    def preprocess_image(self, image):
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 自适应阈值二值化
        binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return processed
    
    def find_text_regions(self, image):
        """查找可能的文字区域"""
        processed = self.preprocess_image(image)
        
        # 查找轮廓
        contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选合适的轮廓
        text_regions = []
        img_height, img_width = image.shape[:2]
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # 基于标注数据的尺寸筛选
            avg_w, avg_h = self.avg_size
            
            # 尺寸筛选条件
            if (w > avg_w * 0.3 and w < avg_w * 2.0 and 
                h > avg_h * 0.3 and h < avg_h * 2.0 and
                w > 100 and h > 50):  # 最小尺寸限制
                
                # 宽高比筛选
                aspect_ratio = w / h
                if 1.5 < aspect_ratio < 8.0:  # SERIAL数字通常比较长
                    text_regions.append((x, y, w, h))
        
        # 按面积排序，优先处理大的区域
        text_regions.sort(key=lambda r: r[2] * r[3], reverse=True)
        
        return text_regions
    
    def extract_text_with_ocr(self, image, region):
        """使用OCR提取文字"""
        x, y, w, h = region
        
        # 确保区域在图片范围内
        img_height, img_width = image.shape[:2]
        x = max(0, x)
        y = max(0, y)
        w = min(w, img_width - x)
        h = min(h, img_height - y)
        
        if w <= 0 or h <= 0:
            return ""
        
        # 提取区域
        roi = image[y:y+h, x:x+w]
        
        # 预处理ROI
        processed_roi = self.preprocess_image(roi)
        
        # 放大图像提高OCR效果
        scale_factor = 3
        enlarged = cv2.resize(processed_roi, None, fx=scale_factor, fy=scale_factor, 
                            interpolation=cv2.INTER_CUBIC)
        
        try:
            # OCR配置
            config = '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-'
            text = pytesseract.image_to_string(enlarged, config=config).strip()
            
            # 清理文本
            text = re.sub(r'[^A-Z0-9-]', '', text.upper())
            
            return text
        except:
            return ""
    
    def is_valid_serial(self, text):
        """验证是否是有效的SERIAL格式"""
        if not text or len(text) < 6:
            return False
        
        # 基于已知SERIAL格式的模式匹配
        patterns = [
            r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914
            r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
            r'^[A-Z]{3}\d{2}-\d{3,4}$',  # MAR23-1055
            r'^[A-Z]{3}\d{2}-\d{4}$',   # SEP23-1153
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                return True
        
        # 通用模式：字母+数字+连字符+数字
        if re.match(r'^[A-Z]{2,4}\d{2,3}-\d{3,4}$', text):
            return True
        
        return False
    
    def recognize_image(self, image_path):
        """识别单张图片中的SERIAL数字"""
        print(f"\n🔍 正在识别: {os.path.basename(image_path)}")

        # 加载图片 - 使用cv2.imdecode处理中文路径
        try:
            # 读取文件为字节流
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)

            # 解码图片
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                print("❌ 无法解码图片")
                return []
        except Exception as e:
            print(f"❌ 加载图片失败: {str(e)}")
            return []
        
        print(f"📐 图片尺寸: {image.shape[1]} x {image.shape[0]}")
        
        # 查找文字区域
        text_regions = self.find_text_regions(image)
        print(f"🔍 找到 {len(text_regions)} 个候选区域")
        
        results = []
        
        for i, region in enumerate(text_regions[:10]):  # 最多处理前10个区域
            x, y, w, h = region
            print(f"   区域 {i+1}: 位置({x},{y}) 大小{w}x{h}")
            
            # OCR识别
            text = self.extract_text_with_ocr(image, region)
            
            if text:
                print(f"      OCR结果: '{text}'")
                
                # 验证是否是有效SERIAL
                if self.is_valid_serial(text):
                    print(f"      ✅ 有效SERIAL: {text}")
                    results.append({
                        'serial': text,
                        'region': region,
                        'confidence': 'high'
                    })
                else:
                    print(f"      ❓ 可能的SERIAL: {text}")
                    results.append({
                        'serial': text,
                        'region': region,
                        'confidence': 'low'
                    })
            else:
                print(f"      ❌ 未识别到文字")
        
        return results
    
    def test_ceshi_folder(self):
        """测试ceshi文件夹中的图片"""
        ceshi_folder = "ceshi"
        if not os.path.exists(ceshi_folder):
            print("❌ 找不到ceshi文件夹")
            return
        
        # 获取图片文件
        image_files = []
        for file in os.listdir(ceshi_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)
        
        if not image_files:
            print("❌ ceshi文件夹中没有图片文件")
            return
        
        print(f"\n🎯 开始测试 {len(image_files)} 张图片")
        print("=" * 60)
        
        all_results = {}
        
        for image_file in image_files:
            image_path = os.path.join(ceshi_folder, image_file)
            results = self.recognize_image(image_path)
            all_results[image_file] = results
        
        # 汇总结果
        self.print_summary(all_results)
        
        # 保存结果
        self.save_results(all_results)
    
    def print_summary(self, results):
        """打印识别结果汇总"""
        print("\n" + "=" * 60)
        print("🎯 识别结果汇总")
        print("=" * 60)
        
        total_images = len(results)
        total_serials = 0
        high_confidence = 0
        
        for filename, serials in results.items():
            print(f"\n📄 {filename}:")
            if serials:
                for i, result in enumerate(serials, 1):
                    serial = result['serial']
                    confidence = result['confidence']
                    region = result['region']
                    
                    if confidence == 'high':
                        print(f"   ✅ {i}. {serial} (高置信度)")
                        high_confidence += 1
                    else:
                        print(f"   ❓ {i}. {serial} (低置信度)")
                    
                    total_serials += 1
            else:
                print("   ❌ 未识别到SERIAL数字")
        
        print(f"\n📊 统计:")
        print(f"   总图片数: {total_images}")
        print(f"   识别到的SERIAL数: {total_serials}")
        print(f"   高置信度: {high_confidence}")
        print(f"   低置信度: {total_serials - high_confidence}")
    
    def save_results(self, results):
        """保存识别结果"""
        output_file = f"recognition_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 识别结果已保存到: {output_file}")

def main():
    print("🎯 SERIAL数字识别测试程序")
    print("基于您的标注数据进行识别")
    print("=" * 50)
    
    recognizer = SerialRecognizer()
    recognizer.test_ceshi_folder()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理dataset文件夹中所有图片的SERIAL数字提取工具
"""

import os
import sys
import glob
import csv
import json
from datetime import datetime
from PIL import Image, ImageEnhance, ImageFilter
import re

class BatchSerialExtractor:
    def __init__(self):
        self.dataset_folder = "dataset"
        self.output_folder = "dataset_results"
        self.results = []
        
        # 创建输出文件夹
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
    
    def get_current_time(self):
        """获取当前时间字符串"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def enhance_image_for_ocr(self, image_path, output_path):
        """增强图片以便更好地进行文字识别"""
        try:
            # 打开图片
            img = Image.open(image_path)
            
            # 转换为RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(2.0)
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(2.0)
            
            # 转换为灰度图
            img = img.convert('L')
            
            # 应用阈值处理
            threshold = 128
            img = img.point(lambda x: 255 if x > threshold else 0, mode='1')
            
            # 保存处理后的图片
            img.save(output_path)
            return True
            
        except Exception as e:
            print(f"处理图片 {image_path} 失败: {str(e)}")
            return False
    
    def try_ocr_recognition(self, image_path):
        """尝试OCR自动识别"""
        try:
            import pytesseract
            from PIL import Image

            # 配置OCR参数
            custom_config = r'--oem 3 --psm 6'

            # 进行OCR识别
            img = Image.open(image_path)
            text = pytesseract.image_to_string(img, config=custom_config)

            return text

        except ImportError:
            return None
        except Exception as e:
            print(f"OCR识别失败: {str(e)}")
            return None

    def try_annotation_based_recognition(self, image_path):
        """基于标注数据的识别"""
        try:
            # 加载标注数据
            annotation_file = "serial_annotations.json"
            if not os.path.exists(annotation_file):
                return None

            with open(annotation_file, 'r', encoding='utf-8') as f:
                annotations = json.load(f)

            filename = os.path.basename(image_path)
            if filename not in annotations:
                return None

            # 如果有标注数据，直接返回标注的SERIAL数字
            boxes = annotations[filename]
            if boxes:
                serials = [box['serial'] for box in boxes]
                print(f"  📋 使用标注数据: {', '.join(serials)}")
                return '\n'.join([f"SERIAL: {serial}" for serial in serials])

            return None

        except Exception as e:
            print(f"读取标注数据失败: {str(e)}")
            return None
    
    def find_serial_numbers(self, text):
        """从文本中查找SERIAL后面的数字"""
        if not text:
            return []
        
        serial_numbers = []
        
        # 将文本按行分割
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 查找包含SERIAL的行
            if 'SERIAL' in line.upper() or 'S/N' in line.upper():
                # 使用正则表达式查找SERIAL后面的数字
                patterns = [
                    r'SERIAL\s*[:\-]?\s*([A-Z0-9]+)',  # SERIAL: 或 SERIAL- 或 SERIAL 后跟数字字母
                    r'S/N\s*[:\-]?\s*([A-Z0-9]+)',     # S/N: 或 S/N- 后跟数字字母
                    r'SERIAL\s+([A-Z0-9]+)',           # SERIAL 空格 数字字母
                    r'SERIAL([A-Z0-9]+)',              # SERIAL直接跟数字字母
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, line.upper())
                    for match in matches:
                        if match and len(match) > 0 and match not in ['SERIAL', 'NUMBER']:
                            serial_numbers.append(match)
                
                # 如果没有找到匹配，尝试提取行中的所有数字字母组合
                if not serial_numbers:
                    # 查找行中所有可能的序列号（数字字母组合）
                    potential_serials = re.findall(r'[A-Z0-9]{3,}', line.upper())
                    for serial in potential_serials:
                        if serial not in ['SERIAL', 'NUMBER', 'SN']:  # 排除关键词本身
                            serial_numbers.append(serial)
        
        return list(set(serial_numbers))  # 去重
    
    def process_single_image(self, image_path):
        """处理单个图片"""
        filename = os.path.basename(image_path)
        print(f"正在处理: {filename}")
        
        result = {
            'filename': filename,
            'filepath': image_path,
            'process_time': self.get_current_time(),
            'status': 'failed',
            'serial_numbers': [],
            'ocr_text': '',
            'error_message': ''
        }
        
        try:
            # 生成处理后图片的路径
            processed_filename = f"processed_{filename}"
            processed_path = os.path.join(self.output_folder, processed_filename)
            
            # 增强图片
            if self.enhance_image_for_ocr(image_path, processed_path):
                result['processed_image'] = processed_path

                # 首先尝试基于标注数据的识别
                annotation_text = self.try_annotation_based_recognition(image_path)
                if annotation_text:
                    result['ocr_text'] = annotation_text
                    result['recognition_method'] = 'annotation'

                    # 查找SERIAL数字
                    serial_numbers = self.find_serial_numbers(annotation_text)
                    if serial_numbers:
                        result['serial_numbers'] = serial_numbers
                        result['status'] = 'success'
                        print(f"  ✅ 找到SERIAL数字: {', '.join(serial_numbers)}")
                    else:
                        result['status'] = 'no_serial_found'
                        print(f"  ⚠️ 标注数据中未找到SERIAL数字")
                else:
                    # 如果没有标注数据，尝试OCR识别
                    ocr_text = self.try_ocr_recognition(processed_path)
                    if ocr_text:
                        result['ocr_text'] = ocr_text
                        result['recognition_method'] = 'ocr'

                        # 查找SERIAL数字
                        serial_numbers = self.find_serial_numbers(ocr_text)
                        if serial_numbers:
                            result['serial_numbers'] = serial_numbers
                            result['status'] = 'success'
                            print(f"  ✅ 找到SERIAL数字: {', '.join(serial_numbers)}")
                        else:
                            result['status'] = 'no_serial_found'
                            print(f"  ⚠️ 未找到SERIAL数字")
                    else:
                        result['status'] = 'ocr_failed'
                        result['error_message'] = 'OCR识别失败'
                        print(f"  ❌ OCR识别失败")
            else:
                result['error_message'] = '图片处理失败'
                print(f"  ❌ 图片处理失败")
                
        except Exception as e:
            result['error_message'] = str(e)
            print(f"  ❌ 处理失败: {str(e)}")
        
        return result
    
    def get_image_files(self):
        """获取dataset文件夹中的所有图片文件"""
        if not os.path.exists(self.dataset_folder):
            print(f"错误：{self.dataset_folder} 文件夹不存在")
            return []
        
        # 支持的图片格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif']
        image_files = []
        
        for ext in extensions:
            pattern = os.path.join(self.dataset_folder, ext)
            image_files.extend(glob.glob(pattern))
            # 也查找大写扩展名
            pattern = os.path.join(self.dataset_folder, ext.upper())
            image_files.extend(glob.glob(pattern))
        
        return sorted(list(set(image_files)))  # 去重并排序
    
    def save_results_to_csv(self):
        """保存结果到CSV文件"""
        csv_path = os.path.join(self.output_folder, "serial_extraction_results.csv")

        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['filename', 'filepath', 'process_time', 'status', 'serial_numbers', 'error_message']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in self.results:
                # 只保留CSV需要的字段
                csv_result = {
                    'filename': result['filename'],
                    'filepath': result['filepath'],
                    'process_time': result['process_time'],
                    'status': result['status'],
                    'serial_numbers': ', '.join(result['serial_numbers']),
                    'error_message': result.get('error_message', '')
                }
                writer.writerow(csv_result)

        print(f"CSV结果已保存到: {csv_path}")
    
    def save_results_to_json(self):
        """保存结果到JSON文件"""
        json_path = os.path.join(self.output_folder, "serial_extraction_results.json")
        
        with open(json_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.results, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"JSON结果已保存到: {json_path}")
    
    def save_summary_report(self):
        """生成汇总报告"""
        report_path = os.path.join(self.output_folder, "extraction_summary.txt")
        
        total_images = len(self.results)
        successful_extractions = len([r for r in self.results if r['status'] == 'success'])
        no_serial_found = len([r for r in self.results if r['status'] == 'no_serial_found'])
        failed_extractions = len([r for r in self.results if r['status'] == 'failed' or r['status'] == 'ocr_failed'])
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("SERIAL数字提取批量处理报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"处理时间: {self.get_current_time()}\n")
            f.write(f"处理文件夹: {self.dataset_folder}\n")
            f.write(f"输出文件夹: {self.output_folder}\n\n")
            
            f.write("处理统计:\n")
            f.write(f"  总图片数量: {total_images}\n")
            f.write(f"  成功提取: {successful_extractions}\n")
            f.write(f"  未找到SERIAL: {no_serial_found}\n")
            f.write(f"  处理失败: {failed_extractions}\n\n")
            
            f.write("成功提取的SERIAL数字:\n")
            f.write("-" * 30 + "\n")
            for result in self.results:
                if result['status'] == 'success':
                    f.write(f"{result['filename']}: {', '.join(result['serial_numbers'])}\n")
            
            f.write("\n未找到SERIAL的图片:\n")
            f.write("-" * 30 + "\n")
            for result in self.results:
                if result['status'] == 'no_serial_found':
                    f.write(f"{result['filename']}\n")
            
            f.write("\n处理失败的图片:\n")
            f.write("-" * 30 + "\n")
            for result in self.results:
                if result['status'] in ['failed', 'ocr_failed']:
                    f.write(f"{result['filename']}: {result['error_message']}\n")
        
        print(f"汇总报告已保存到: {report_path}")
    
    def run_batch_processing(self):
        """运行批量处理"""
        print("=" * 60)
        print("Dataset文件夹SERIAL数字批量提取工具")
        print("=" * 60)
        
        # 获取所有图片文件
        image_files = self.get_image_files()
        
        if not image_files:
            print(f"在 {self.dataset_folder} 文件夹中没有找到图片文件")
            return
        
        print(f"找到 {len(image_files)} 个图片文件")
        print(f"输出文件夹: {self.output_folder}")
        print("-" * 60)
        
        # 处理每个图片
        for i, image_path in enumerate(image_files, 1):
            print(f"[{i}/{len(image_files)}] ", end="")
            result = self.process_single_image(image_path)
            self.results.append(result)
        
        print("-" * 60)
        print("批量处理完成！")
        
        # 保存结果
        self.save_results_to_csv()
        self.save_results_to_json()
        self.save_summary_report()
        
        # 显示统计信息
        successful = len([r for r in self.results if r['status'] == 'success'])
        print(f"\n处理统计:")
        print(f"  总计: {len(self.results)} 个图片")
        print(f"  成功提取: {successful} 个")
        print(f"  成功率: {successful/len(self.results)*100:.1f}%")

def main():
    """主函数"""
    extractor = BatchSerialExtractor()
    extractor.run_batch_processing()
    
    print("\n所有结果文件已保存到 dataset_results 文件夹中")
    input("按Enter键退出...")

if __name__ == "__main__":
    main()

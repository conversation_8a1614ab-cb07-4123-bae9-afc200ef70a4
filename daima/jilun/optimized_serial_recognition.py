#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的SERIAL识别算法
基于用户确认的最佳方法：CLAHE + 自适应阈值
已知SERIAL: JAN23-7914, JUL11-0087, JAN23-7913, SEP23-1153, FE823-1036, MAR23-1055, JUN23-1105
"""

import cv2
import numpy as np
import os
import json
import pytesseract
import re
from datetime import datetime

class OptimizedSerialRecognizer:
    def __init__(self):
        self.known_serials = [
            "JAN23-7914", "JUL11-0087", "JAN23-7913", 
            "SEP23-1153", "FE823-1036", "MAR23-1055", "JUN23-1105"
        ]
        self.month_patterns = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN",
                              "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"]
    
    def load_image(self, image_path):
        """加载图片"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            print(f"❌ 加载图片失败: {str(e)}")
            return None
    
    def apply_best_preprocessing(self, image):
        """应用最佳预处理方法：CLAHE + 自适应阈值"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # CLAHE增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # 自适应阈值
        binary = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        return binary, enhanced, gray
    
    def find_serial_regions(self, binary_image):
        """查找可能的SERIAL区域"""
        # 形态学操作连接字符
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 5))
        connected = cv2.morphologyEx(binary_image, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        candidates = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # 基于已知SERIAL特征筛选
            # 宽度通常在200-600像素，高度在30-100像素
            if 200 <= w <= 600 and 30 <= h <= 100:
                aspect_ratio = w / h
                # 宽高比通常在3-15之间
                if 3 <= aspect_ratio <= 15:
                    candidates.append((x, y, w, h, aspect_ratio))
        
        # 按面积排序，优先处理较大的区域
        candidates.sort(key=lambda x: x[2] * x[3], reverse=True)
        return candidates
    
    def enhanced_ocr(self, roi):
        """增强的OCR识别"""
        results = []
        
        # 多种放大倍数
        scale_factors = [2, 3, 4, 5]
        
        for scale in scale_factors:
            # 放大图像
            enlarged = cv2.resize(roi, None, fx=scale, fy=scale, 
                                interpolation=cv2.INTER_CUBIC)
            
            # 多种OCR配置
            configs = [
                # 专门针对SERIAL格式
                '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-',
                '--oem 3 --psm 7 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-',
                '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-',
                # 默认配置
                '--oem 3 --psm 8',
                '--oem 3 --psm 7',
            ]
            
            for config in configs:
                try:
                    text = pytesseract.image_to_string(enlarged, config=config).strip()
                    clean_text = re.sub(r'[^A-Z0-9-]', '', text.upper())
                    
                    if clean_text and len(clean_text) >= 6:
                        confidence = self.calculate_confidence(clean_text)
                        results.append({
                            'text': clean_text,
                            'confidence': confidence,
                            'scale': scale,
                            'config': config,
                            'raw_text': text
                        })
                except:
                    continue
        
        # 按置信度排序
        results.sort(key=lambda x: x['confidence'], reverse=True)
        return results
    
    def calculate_confidence(self, text):
        """计算识别置信度"""
        confidence = 0
        
        # 长度检查
        if 8 <= len(text) <= 12:
            confidence += 20
        
        # 格式检查
        if self.is_valid_serial_format(text):
            confidence += 50
        
        # 月份检查
        if len(text) >= 3:
            month_part = text[:3]
            if month_part in self.month_patterns:
                confidence += 30
        
        # 与已知SERIAL的相似度
        similarity_bonus = self.calculate_similarity_bonus(text)
        confidence += similarity_bonus
        
        return min(confidence, 100)
    
    def calculate_similarity_bonus(self, text):
        """计算与已知SERIAL的相似度奖励"""
        max_bonus = 0
        
        for known_serial in self.known_serials:
            # 检查月份部分
            if len(text) >= 3 and len(known_serial) >= 3:
                if text[:3] == known_serial[:3]:
                    max_bonus = max(max_bonus, 15)
            
            # 检查年份部分
            if len(text) >= 5 and len(known_serial) >= 5:
                if text[3:5] == known_serial[3:5]:
                    max_bonus = max(max_bonus, 10)
            
            # 检查整体格式相似度
            if len(text) == len(known_serial):
                max_bonus = max(max_bonus, 5)
        
        return max_bonus
    
    def is_valid_serial_format(self, text):
        """验证SERIAL格式"""
        patterns = [
            r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914, JUN23-1105
            r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
            r'^[A-Z]{3}\d{2}-\d{3}$',  # 3位数字结尾的变体
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                return True
        return False
    
    def recognize_image(self, image_path):
        """识别单张图片"""
        print(f"\n🎯 识别图片: {os.path.basename(image_path)}")
        print("-" * 50)
        
        # 加载图片
        image = self.load_image(image_path)
        if image is None:
            return []
        
        print(f"📐 图片尺寸: {image.shape[1]} x {image.shape[0]}")
        
        # 应用最佳预处理
        binary, enhanced, gray = self.apply_best_preprocessing(image)
        print("✅ 已应用CLAHE + 自适应阈值预处理")
        
        # 查找候选区域
        candidates = self.find_serial_regions(binary)
        print(f"🔍 找到 {len(candidates)} 个候选区域")
        
        all_results = []
        
        for i, (x, y, w, h, ratio) in enumerate(candidates, 1):
            print(f"\n区域 {i}: ({x},{y}) {w}x{h} 宽高比:{ratio:.2f}")
            
            # 提取区域
            roi = binary[y:y+h, x:x+w]
            
            # OCR识别
            ocr_results = self.enhanced_ocr(roi)
            
            if ocr_results:
                best_result = ocr_results[0]
                print(f"   最佳识别: {best_result['text']} (置信度: {best_result['confidence']})")
                
                # 添加位置信息
                best_result.update({
                    'region': (x, y, w, h),
                    'region_index': i
                })
                all_results.append(best_result)
            else:
                print(f"   ❌ 未识别到有效文字")
        
        # 按置信度排序
        all_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        return all_results
    
    def test_all_images(self):
        """测试所有ceshi文件夹中的图片"""
        ceshi_folder = "ceshi"
        if not os.path.exists(ceshi_folder):
            print("❌ 找不到ceshi文件夹")
            return
        
        image_files = []
        for file in os.listdir(ceshi_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)
        
        if not image_files:
            print("❌ ceshi文件夹中没有图片")
            return
        
        print(f"🎯 优化的SERIAL识别测试")
        print(f"基于最佳方法: CLAHE + 自适应阈值")
        print("=" * 60)
        
        results = {}
        
        for image_file in image_files:
            image_path = os.path.join(ceshi_folder, image_file)
            recognition_results = self.recognize_image(image_path)
            results[image_file] = recognition_results
        
        # 汇总结果
        print(f"\n📊 识别结果汇总:")
        print("=" * 60)
        
        success_count = 0
        for image_file, recognition_results in results.items():
            print(f"\n📄 {image_file}:")
            if recognition_results:
                best = recognition_results[0]
                if best['confidence'] >= 70:
                    print(f"   ✅ {best['text']} (置信度: {best['confidence']})")
                    success_count += 1
                else:
                    print(f"   ❓ {best['text']} (置信度: {best['confidence']} - 较低)")
            else:
                print(f"   ❌ 未识别到SERIAL")
        
        print(f"\n🎉 总体结果:")
        print(f"   成功识别: {success_count}/{len(image_files)} ({success_count/len(image_files)*100:.1f}%)")
        
        # 保存结果
        output_file = f"optimized_recognition_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 详细结果已保存到: {output_file}")
        
        return results

def main():
    recognizer = OptimizedSerialRecognizer()
    recognizer.test_all_images()

if __name__ == "__main__":
    main()

# SERIAL数字标注工具

专门用于标注dataset文件夹中SERIAL后面数字的专业标注工具。

## 功能特点

- 🎯 **专门针对SERIAL数字标注**：专为SERIAL后面的数字/字母组合设计
- 🔄 **图片旋转功能**：支持90度旋转，方便标注不同角度的图片
- 📊 **实时显示标注**：在原图上实时显示标注框和内容
- 💾 **自动保存**：标注数据自动保存，无需担心丢失
- 📦 **导出训练数据**：一键导出训练用的图片切片
- 🖼️ **生成标注图片**：生成带标注框的图片用于检查
- 📝 **支持多标注**：一张图片可以标注多个SERIAL数字

## 文件说明

### 主要文件
- `serial_number_annotation_tool.py` - 主标注工具程序
- `run_annotation_tool.bat` - 启动脚本（推荐使用）
- `start_tool.bat` - 简化启动脚本
- `dataset/` - 存放要标注的图片文件夹

### 生成的文件和文件夹
- `serial_number_annotations.json` - 标注数据文件
- `serial_training_data/` - 导出的训练数据文件夹
- `annotated_images/` - 带标注的图片文件夹
  - `annotated_*.jpg` - 带标注框的图片文件

### 说明文件
- `SERIAL数字标注工具使用指南.txt` - 详细使用说明
- `测试标注工具.py` - 工具状态检查脚本

## 快速开始

### 1. 准备工作
1. 确保安装了Python（建议3.7+）
2. 将要标注的图片放入`dataset`文件夹
3. 支持的图片格式：jpg, jpeg, png, bmp

### 2. 启动工具
双击 `run_annotation_tool.bat` 文件

### 3. 标注流程
1. 在下拉框中选择要标注的图片
2. 如果图片角度不正，使用旋转功能调整
3. 在图片上拖拽鼠标画框选中SERIAL数字
4. 在左侧输入框中输入识别出的数字
5. 点击"添加标注"保存
6. 重复步骤3-5标注所有SERIAL数字
7. 完成后点击"导出训练数据"

## 标注技巧

### 画框技巧
- 尽量紧贴数字边缘，不要包含太多背景
- 确保数字完整，不要遗漏任何字符
- 框选时要包含完整的数字序列

### 输入格式
- 只输入SERIAL后面的数字/字母部分
- 保持原有格式，包括连字符等
- 例如：`JAN23-7914`、`JUL11-0087`

### 旋转使用
- 如果SERIAL数字是倾斜或倒置的，先旋转图片
- 建议将数字调整到水平方向便于阅读
- 旋转不会影响最终标注坐标

## 数据格式

### 标注数据结构
```json
{
  "1.jpg": [
    {
      "x": 1463,
      "y": 1085,
      "w": 1535,
      "h": 435,
      "serial": "JAN23-7914",
      "timestamp": "2024-07-04T10:30:00"
    }
  ]
}
```

### 训练数据
- 每个标注区域会生成一个单独的图片文件
- 文件命名格式：`原图名_serial_序号_数字内容.jpg`
- 同时生成`training_info.json`包含详细信息

## 常见问题

### Q: bat文件双击后显示乱码？
A: 使用`run_annotation_tool.bat`，这个文件使用纯ASCII编码，避免了中文乱码问题。

### Q: 提示"Python not found"？
A: 需要安装Python并确保添加到PATH环境变量中。

### Q: 画框时提示"标注框太小"？
A: 请画更大的框，确保宽度和高度都大于10像素。

### Q: 如何删除错误的标注？
A: 在左侧标注列表中选择要删除的标注，点击"删除选中标注"。

### Q: 旋转后标注坐标会错误吗？
A: 不会，工具会自动处理坐标转换，确保标注数据基于原图。

## 系统要求

- Windows 7/8/10/11
- Python 3.7+
- 必需的Python库：
  - tkinter（通常随Python安装）
  - opencv-python
  - numpy
  - Pillow

## 技术支持

如果遇到问题：
1. 运行`测试标注工具.py`检查系统状态
2. 确保所有文件在同一文件夹中
3. 检查dataset文件夹是否存在且包含图片
4. 确保Python和必需库正确安装

## 更新日志

### v1.0
- 初始版本
- 基本标注功能
- 图片旋转支持
- 自动保存功能
- 训练数据导出
- 标注图片生成

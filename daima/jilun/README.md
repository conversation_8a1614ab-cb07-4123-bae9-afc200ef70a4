# SERIAL数字提取工具

## 功能说明
本工具专门用于从图片中提取SERIAL后面的数字，特别针对8.jpg图片进行了优化。

## 测试结果
✅ **成功提取**: 从8.jpg中成功识别出SERIAL数字 `JAN23-7914`

## 文件说明

### 主要程序文件
- **R1.py** - 完整版GUI工具（带图形界面）
- **extract_8jpg.py** - 专门处理8.jpg的简化工具
- **serial_extractor_simple.py** - 通用简化版命令行工具

### 启动脚本
- **简单启动器.bat** - 推荐使用，一键启动extract_8jpg.py
- **启动SERIAL提取工具.bat** - 启动完整版GUI工具

### 输出文件
- **processed_8.jpg** - 处理后的图片（增强对比度和清晰度）
- **serial_result.txt** - 提取结果保存文件

### 说明文档
- **使用说明.txt** - 详细使用说明
- **README.md** - 本文件

## 快速使用

### 方法一：双击启动（推荐）
1. 确保8.jpg文件在当前文件夹中
2. 双击 `简单启动器.bat`
3. 按照提示操作

### 方法二：命令行运行
```bash
python extract_8jpg.py
```

## 工具特点

### 自动识别
- 使用OCR技术自动识别图片中的文字
- 智能查找SERIAL关键词
- 自动提取相关数字

### 手动辅助
- 当自动识别失败时，提供手动识别指导
- 生成增强后的图片便于人工识别
- 支持用户手动输入识别结果

### 结果保存
- 自动保存识别结果到serial_result.txt
- 记录识别时间和方法
- 便于后续查看和使用

## 系统要求
- Windows 7/8/10/11
- Python 3.6或更高版本
- 推荐安装：Pillow库（图像处理）
- 可选安装：pytesseract库（OCR功能）

## 安装说明

### 自动安装
运行启动脚本时会自动检查并安装必要的库。

### 手动安装
```bash
pip install Pillow pytesseract
```

## 使用示例

### 成功案例
- **输入**: 8.jpg（包含SERIAL信息的图片）
- **输出**: JAN23-7914
- **处理时间**: 约10-30秒

### 支持的SERIAL格式
- SERIAL: 123456
- SERIAL NO: ABC123
- SERIAL NUMBER: 789XYZ
- S/N: 456789
- 其他包含SERIAL关键词的格式

## 故障排除

### 常见问题
1. **Python未安装**: 访问 https://www.python.org/downloads/ 下载安装
2. **库安装失败**: 检查网络连接，使用管理员权限运行
3. **识别不准确**: 检查图片质量，确保文字清晰

### 提高识别准确率的建议
- 使用高分辨率图片
- 确保SERIAL文字清晰可见
- 避免图片过度模糊或倾斜
- 良好的光照条件

## 技术实现

### 图像处理
- 对比度增强
- 锐度提升
- 灰度转换
- 二值化处理

### OCR识别
- 使用Tesseract OCR引擎
- 自定义识别参数
- 多种模式尝试

### 文本处理
- 正则表达式匹配
- 关键词搜索
- 格式标准化

## 新增功能 - 标注工具

### 标注工具套件
- **serial_annotation_tool.py** - 图形化标注工具
- **启动标注工具.bat** - 一键启动标注工具
- **标注工具使用指南.txt** - 详细使用说明

### 标注工具功能
- 🖼️ 图片浏览和选择
- 🖱️ 鼠标画框标注SERIAL位置
- ✏️ 手动输入SERIAL数字内容
- 💾 标注数据自动保存
- 📦 训练数据导出功能
- 🔄 支持多图片批量标注

### 改进的批量处理
- **batch_extract_dataset.py** - 支持标注数据的批量处理
- **批量处理dataset.bat** - 一键启动批量处理
- 优先使用标注数据进行识别
- 自动回退到OCR识别

### 使用流程
1. **标注阶段**: 使用标注工具为图片画框并输入SERIAL数字
2. **训练阶段**: 导出训练数据，生成标注样本
3. **识别阶段**: 批量处理时优先使用标注数据

## 版本信息
- **版本**: 2.0
- **更新日期**: 2024年12月
- **测试状态**: ✅ 已通过8.jpg测试，✅ 支持dataset批量处理
- **支持格式**: JPG, JPEG, PNG, BMP, TIFF
- **新增**: 标注工具和基于标注的识别

## 开发说明
本工具基于Python开发，使用了以下主要库：
- PIL/Pillow: 图像处理
- pytesseract: OCR文字识别
- tkinter: GUI界面（完整版和标注工具）
- opencv-python: 图像处理和标注工具
- numpy: 数值计算
- re: 正则表达式处理

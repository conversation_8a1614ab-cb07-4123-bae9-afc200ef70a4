#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片分析和调试工具
帮助诊断为什么OCR识别失败
"""

import cv2
import numpy as np
import os
import matplotlib.pyplot as plt
from matplotlib import rcParams
import pytesseract

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

class ImageAnalyzer:
    def __init__(self):
        pass
    
    def load_image(self, image_path):
        """加载图片"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            print(f"❌ 加载图片失败: {str(e)}")
            return None
    
    def analyze_image(self, image_path):
        """分析图片"""
        print(f"🔍 分析图片: {os.path.basename(image_path)}")
        print("=" * 60)
        
        image = self.load_image(image_path)
        if image is None:
            return
        
        # 基本信息
        height, width = image.shape[:2]
        print(f"📐 图片尺寸: {width} x {height}")
        print(f"📊 图片大小: {image.size} 像素")
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 分析图片质量
        self.analyze_quality(gray)
        
        # 尝试不同的预处理方法
        self.try_different_preprocessing(gray, image_path)
        
        # 保存处理后的图片用于检查
        self.save_processed_images(gray, image_path)
    
    def analyze_quality(self, gray):
        """分析图片质量"""
        print("\n📊 图片质量分析:")
        print("-" * 30)
        
        # 亮度分析
        mean_brightness = np.mean(gray)
        print(f"💡 平均亮度: {mean_brightness:.1f} (0-255)")
        
        # 对比度分析
        contrast = np.std(gray)
        print(f"🌓 对比度: {contrast:.1f}")
        
        # 清晰度分析（拉普拉斯方差）
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        print(f"🔍 清晰度: {laplacian_var:.1f}")
        
        # 质量评估
        if mean_brightness < 50:
            print("⚠️  图片较暗，可能影响识别")
        elif mean_brightness > 200:
            print("⚠️  图片较亮，可能过曝")
        else:
            print("✅ 亮度正常")
        
        if contrast < 30:
            print("⚠️  对比度较低，可能影响识别")
        else:
            print("✅ 对比度正常")
        
        if laplacian_var < 100:
            print("⚠️  图片可能模糊")
        else:
            print("✅ 图片清晰度正常")
    
    def try_different_preprocessing(self, gray, image_path):
        """尝试不同的预处理方法"""
        print("\n🔧 尝试不同预处理方法:")
        print("-" * 30)
        
        methods = {
            "原图": gray,
            "高斯模糊": cv2.GaussianBlur(gray, (3, 3), 0),
            "双边滤波": cv2.bilateralFilter(gray, 9, 75, 75),
            "CLAHE增强": cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(gray),
        }
        
        # 对每种预处理方法尝试不同的二值化
        for method_name, processed in methods.items():
            print(f"\n{method_name}:")
            
            # OTSU二值化
            _, otsu = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            otsu_text = self.quick_ocr(otsu)
            print(f"  OTSU: {otsu_text if otsu_text else '无识别结果'}")
            
            # 自适应阈值
            adaptive = cv2.adaptiveThreshold(processed, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                           cv2.THRESH_BINARY, 11, 2)
            adaptive_text = self.quick_ocr(adaptive)
            print(f"  自适应: {adaptive_text if adaptive_text else '无识别结果'}")
            
            # 反色OTSU
            _, otsu_inv = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            otsu_inv_text = self.quick_ocr(otsu_inv)
            print(f"  反色OTSU: {otsu_inv_text if otsu_inv_text else '无识别结果'}")
    
    def quick_ocr(self, image):
        """快速OCR测试"""
        try:
            # 放大图像
            enlarged = cv2.resize(image, None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)
            
            # OCR
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-'
            text = pytesseract.image_to_string(enlarged, config=config).strip()
            
            # 查找可能的SERIAL
            lines = text.split('\n')
            for line in lines:
                if 'SERIAL' in line.upper():
                    return line.strip()
            
            # 查找符合格式的文字
            import re
            for line in lines:
                clean_line = re.sub(r'[^A-Z0-9-]', '', line.upper())
                if len(clean_line) >= 6 and re.search(r'[A-Z]{2,4}\d{2,3}-\d{3,4}', clean_line):
                    return clean_line
            
            return text.replace('\n', ' ').strip() if text else ""
        except:
            return ""
    
    def save_processed_images(self, gray, image_path):
        """保存处理后的图片"""
        print("\n💾 保存处理后的图片用于检查...")
        
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        # 创建输出文件夹
        output_dir = "debug_images"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 不同的处理方法
        processes = {
            "01_original": gray,
            "02_gaussian": cv2.GaussianBlur(gray, (3, 3), 0),
            "03_bilateral": cv2.bilateralFilter(gray, 9, 75, 75),
            "04_clahe": cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(gray),
        }
        
        for process_name, processed in processes.items():
            # 保存预处理结果
            cv2.imwrite(f"{output_dir}/{base_name}_{process_name}.jpg", processed)
            
            # 保存二值化结果
            _, otsu = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            cv2.imwrite(f"{output_dir}/{base_name}_{process_name}_otsu.jpg", otsu)
            
            adaptive = cv2.adaptiveThreshold(processed, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                           cv2.THRESH_BINARY, 11, 2)
            cv2.imwrite(f"{output_dir}/{base_name}_{process_name}_adaptive.jpg", adaptive)
        
        print(f"✅ 处理后的图片已保存到 {output_dir} 文件夹")
        print("   您可以查看这些图片，看看哪种处理效果最好")
    
    def suggest_manual_methods(self):
        """建议手动方法"""
        print("\n💡 建议的替代方法:")
        print("=" * 60)
        print("1. 📱 手机拍照重新拍摄")
        print("   - 确保光线充足")
        print("   - 垂直拍摄，避免倾斜")
        print("   - 尽量靠近SERIAL区域")
        print()
        print("2. 🖼️  图片编辑软件处理")
        print("   - 调整亮度和对比度")
        print("   - 锐化处理")
        print("   - 裁剪出SERIAL区域")
        print()
        print("3. 🔍 在线OCR工具测试")
        print("   - 百度OCR")
        print("   - 腾讯OCR")
        print("   - Google Vision API")
        print()
        print("4. 👁️  人工识别")
        print("   - 如果您能清楚看到，可以直接告诉我数字")
        print("   - 我可以帮您验证格式是否正确")
        print()
        print("5. 📊 使用模板匹配")
        print("   - 基于您已标注的SERIAL样本")
        print("   - 查找相似的区域")

def main():
    analyzer = ImageAnalyzer()
    
    # 分析测试图片
    test_image = "ceshi/微信图片_20250704123039.jpg"
    
    if os.path.exists(test_image):
        analyzer.analyze_image(test_image)
    else:
        print(f"❌ 找不到测试图片: {test_image}")
    
    analyzer.suggest_manual_methods()

if __name__ == "__main__":
    main()

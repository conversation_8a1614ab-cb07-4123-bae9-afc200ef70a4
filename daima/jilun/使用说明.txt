SERIAL数字提取工具 - 使用说明
=====================================

功能说明：
---------
本工具用于从图片中自动识别和提取SERIAL后面的数字。
支持多种图片格式：JPG、PNG、BMP、TIFF等。

文件说明：
---------
1. R1.py - 完整版GUI工具（推荐使用）
2. serial_extractor_simple.py - 简化版命令行工具
3. 启动SERIAL提取工具.bat - 一键启动脚本
4. 8.jpg - 示例图片文件

使用方法：
---------

方法一：使用一键启动脚本（推荐）
1. 将要处理的图片文件放在与工具相同的文件夹中
2. 双击"启动SERIAL提取工具.bat"
3. 程序会自动检查并安装必要的库
4. 按照界面提示操作

方法二：直接运行Python脚本
1. 确保已安装Python 3.6或更高版本
2. 安装必要的库：
   pip install opencv-python numpy Pillow pytesseract tkinter
3. 运行完整版：python R1.py
   或运行简化版：python serial_extractor_simple.py

系统要求：
---------
- Windows 7/8/10/11
- Python 3.6或更高版本
- 至少100MB可用磁盘空间

可选组件：
---------
为了获得最佳OCR识别效果，建议安装Tesseract OCR：
1. 下载Tesseract OCR：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径：C:\Program Files\Tesseract-OCR\
3. 重新运行工具

使用技巧：
---------
1. 图片质量越高，识别准确率越高
2. 确保SERIAL文字清晰可见
3. 避免图片过度模糊或倾斜
4. 如果自动识别失败，可以查看处理后的图片手动识别

故障排除：
---------
1. 如果提示"找不到Python"：
   - 请先安装Python：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. 如果库安装失败：
   - 检查网络连接
   - 尝试使用管理员权限运行

3. 如果OCR识别不准确：
   - 尝试调整图片亮度和对比度
   - 确保图片中的文字足够清晰
   - 安装Tesseract OCR以获得更好的识别效果

4. 如果程序无法启动：
   - 检查文件是否完整
   - 确保所有文件在同一文件夹中

联系支持：
---------
如果遇到问题，请检查：
1. Python版本是否正确
2. 所有必要的库是否已安装
3. 图片文件是否存在且格式正确

版本信息：
---------
版本：1.0
更新日期：2024年12月
支持的图片格式：JPG, JPEG, PNG, BMP, TIFF
支持的操作系统：Windows 7/8/10/11

SERIAL数字标注工具使用指南
===============================

工具目的：
---------
通过手动标注SERIAL数字的位置和内容，为机器学习提供训练数据，
从而提高自动识别的准确率。

启动方法：
---------
1. 双击"启动标注工具.bat"
2. 或者运行命令：python serial_annotation_tool.py

界面说明：
---------

左侧控制面板：
1. 图片选择区域
   - 下拉框：选择要标注的图片
   - 上一张/下一张：快速切换图片
   - 浏览文件：选择其他图片文件

2. 标注信息区域
   - SERIAL数字输入框：输入识别出的SERIAL数字
   - 添加标注按钮：保存当前标注

3. 当前图片标注列表
   - 显示当前图片的所有标注
   - 可以选择并删除标注

4. 操作按钮
   - 保存标注：保存所有标注到文件
   - 导出训练数据：生成训练样本
   - 清除当前标注：删除当前图片的所有标注

右侧图片区域：
- 显示当前选中的图片
- 可以用鼠标画框标注SERIAL数字位置

标注步骤：
---------

1. 选择图片
   - 在左侧下拉框中选择要标注的图片
   - 图片会显示在右侧区域

2. 画框标注
   - 在右侧图片上，用鼠标左键按住并拖拽
   - 画出包含SERIAL数字的矩形框
   - 松开鼠标后会提示输入SERIAL数字

3. 输入SERIAL数字
   - 在左侧"SERIAL数字"输入框中输入识别出的数字
   - 点击"添加标注"按钮保存

4. 重复标注
   - 如果一张图片有多个SERIAL数字，重复步骤2-3
   - 每个SERIAL数字都需要单独标注

5. 保存标注
   - 完成一张或多张图片的标注后
   - 点击"保存标注"按钮保存到文件

6. 导出训练数据
   - 完成所有标注后，点击"导出训练数据"
   - 会生成training_data文件夹，包含所有标注区域的图片

标注技巧：
---------

1. 框选范围
   - 尽量紧贴SERIAL数字，不要包含太多背景
   - 确保数字完整，不要遗漏任何字符

2. 输入格式
   - 按照图片中显示的内容输入，保持原始格式
   - 包括字母、数字、连字符等所有字符
   - 例如：JAN23-7914、ABC123、S123456等

3. 多个SERIAL
   - 如果一张图片有多个SERIAL数字，分别标注
   - 每个都要画独立的框并输入对应的数字

4. 质量控制
   - 标注前确保图片清晰可见
   - 如果数字模糊不清，可以跳过该图片
   - 确保输入的数字与图片中显示的完全一致

文件说明：
---------

生成的文件：
1. serial_annotations.json - 标注数据文件
2. training_data/ - 训练数据文件夹
   - 包含每个标注区域的单独图片
   - training_info.json - 训练信息汇总

标注数据格式：
{
  "图片文件名": [
    {
      "x": 100,        // 标注框左上角X坐标
      "y": 50,         // 标注框左上角Y坐标
      "w": 200,        // 标注框宽度
      "h": 30,         // 标注框高度
      "serial": "JAN23-7914",  // SERIAL数字内容
      "timestamp": "2024-12-17T10:30:00"  // 标注时间
    }
  ]
}

使用标注数据：
-----------

标注完成后，批量处理工具会自动使用标注数据：
1. 运行"批量处理dataset.bat"
2. 工具会优先使用标注数据进行识别
3. 对于有标注的图片，直接返回标注结果
4. 对于没有标注的图片，仍使用OCR自动识别

注意事项：
---------

1. 标注数据会自动保存到serial_annotations.json文件
2. 请定期备份标注数据，避免意外丢失
3. 标注质量直接影响后续识别准确率
4. 建议至少标注10-20张图片作为训练样本
5. 如果发现标注错误，可以重新标注覆盖

故障排除：
---------

1. 工具无法启动
   - 检查Python是否正确安装
   - 确保所有依赖库已安装

2. 图片无法显示
   - 检查图片文件是否损坏
   - 确保图片格式受支持（JPG、PNG等）

3. 标注无法保存
   - 检查文件夹写入权限
   - 确保磁盘空间充足

4. 画框不响应
   - 确保图片已正确加载
   - 尝试重新选择图片

联系支持：
---------
如果遇到问题，请检查：
1. 所有文件是否在同一文件夹中
2. Python和依赖库是否正确安装
3. 图片文件是否完整且格式正确

[{"filename": "1.jpg", "filepath": "dataset\\1.jpg", "process_time": "2025-07-04 11:36:40", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "BUA\n", "error_message": "", "processed_image": "dataset_results\\processed_1.jpg"}, {"filename": "2.png", "filepath": "dataset\\2.png", "process_time": "2025-07-04 11:36:41", "status": "ocr_failed", "serial_numbers": [], "ocr_text": "", "error_message": "OCR识别失败", "processed_image": "dataset_results\\processed_2.png"}, {"filename": "3.jpg", "filepath": "dataset\\3.jpg", "process_time": "2025-07-04 11:36:42", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "a\nCan } y\n: i\nNew of\nerrr .\n» i a\nmanana! .\ni #\nt_' |\nLig onal\n‘ j a\ney a . i -\n4 Cd \"| .\nwife\n. ° 4 : ot\nj i a\n4 y Lyf Dee\n‘ j : wey\noe 4 : if yto*\na p 2\n, j Ma\na 4 wate\nme Ug ae\n‘Cz ‘ ; eave -\n- 4 Lyte 2s\ncr d iiighvt\nyi! F eae\nbec Pigee +\nape F ne ea\n‘¢ dR eae\nJe Y . Ate Di\nbes Z Re ye\nSe Fi Ae BAL\no> ql BEC\n— 4 REY fn ore\nde” q arate here a\n: ES BOE .\n3 ayn <PERSON>\n¢ o ee poe\nd ld wee oe 7\n/ j Seager\nE RRS praee .\n/ on 4 , ia or . .\n/ BENS ea ie\ni “ Pa aaa .\n‘ J SS oeet ees -\n; PP oy een\n: Car Sc reer\n: if Bea a\nj usta aT +\nBg\n2 Fe re * Bes\nMe pie\nj Soe\now\nUg\noe\nhy ie\nwae OP\na,\nFs \\\nYe\na\n4,\nee\n' i\n# u\nae\n", "error_message": "", "processed_image": "dataset_results\\processed_3.jpg"}, {"filename": "4.jpg", "filepath": "dataset\\4.jpg", "process_time": "2025-07-04 11:36:43", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "a\npf\n- @\n’\nq\n$\nf\n.\n¥\n¢\n", "error_message": "", "processed_image": "dataset_results\\processed_4.jpg"}, {"filename": "5.jpg", "filepath": "dataset\\5.jpg", "process_time": "2025-07-04 11:36:43", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "“ e\nPoa awe 4 2\n: = gx : “\n2 -¢\n\nQ S ; . . . ba rs § . ‘ %\n\n: Bye 8 a eo Fa a we\n\nae a ‘ Gi oa + 7\nae Bue nore © we ® diy Sone po\n\nsuns Lea es rg a ee aa ae vos ? ; ;\nin @ ! eo. ie m4 ” ie meg el\n", "error_message": "", "processed_image": "dataset_results\\processed_5.jpg"}, {"filename": "6.jpg", "filepath": "dataset\\6.jpg", "process_time": "2025-07-04 11:36:45", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "2\nkee «\nae pom q\n. EB Lo\n’ j\n>. di ‘ “=\nI a q\nf 5 E\nfg j .\n*\n{\n‘\n> OR .\nBre\nmee :*\n% E bd\nLf {\na “\nee tee\nvd tts\nae\nEH\nPL & ,\na . .\nSe Ea @ .\n* fad f .\n‘ ~\ney : .\nss. a ed\n= Siu tern po eg ‘\n3 oes\n4 roel k as\naga! 4 means = ed .\nis boge ae 2S\nJ GS Wire 3 a gees , ‘\nDae 1 Brat Posse ee °\nae a gS é Bees oe ete\n_ ) wm go £ 4 f Eye ak as a itd a\na EA 1 he eee\nPate 15, ee Fe bef\nke Ure Ee\nshalt. Pee Tg #\n& ing STE ES eRe oo\nWha ee Ege De .\nma WO gether Wiese\n: ao LES ae .\nved, <PERSON>”\n«Ane me PET ERE A .\nite PRP any $07. > aye\n) EN age ene\ngo <9%, on she PI ae obs ‘\nfs; weed BER sit hSy ob eS *\nB agrt ‘, eae Ba? he eae: oe\nFate ee HN Bag es\nfang Sib eBae 2S\ngo Ms Br ae Be en .\nne PAS te ng aeT: Cie Se eres ts\nre an: pon\nBy ae Tee Se eh a -\nfre, Bae as tease As\n£8 og DORIS ae Ee\nee Hes, Cee ae! SUT .\nBathe Gaeta oo\n> Pyaing ae iced Sa ee to\nPgs areata TREE as\nAge an Meese =\nsf Af. vie? RUT\no : 2 Went | 6\nfay fi : oO Mer 2\na any bei sies s *\nfey Ee\na fag if . oe\n$ hi ph hy oly : hep\ni Eee : OME pAD oa\nFr FB he ee greg s\nsh ol igh Bi lle yo a\nfd i PURER kei sig PE}\nF pce GEESE ERE RD Vet Panik\nPE han ps Oe aU NE Le aS\n4 ht a} PRAY. ne 3 aie Mele\nCS TIN DOT SRS Sai uinice a Se\nfi eae cant FU te ae Ciera yw,\n/ Say Save Sok\n3 LE OY MNOS ee\n£ tng Bay en fant eae ‘\nfb RRA CRE EGS rf\nRN SUR Cap ies & Jia Sa”\nEs eae y ie Ne RE rey ,\nAwe fares iy 9 aN Lae ice\n# riaceh pM ZN oN Se oe\n7 a an watt APS aa Sa\nPer Ws ie Gea\nBA EN Ae RS a ea\nfC OCRARE TE ES He ea\n4S eee uy APA ROUSS ROR\nBi en gS Tees or\nBes OT ea\n| A ened sheer ite bad POS .\nme 4 RODE Ny ATK py hid é\nae gh bv ald Sa a ae °\nA Bae ANP EAS ae HA OU\nJ eS ae 1s An\nSea aaa) LN ce en hoo\nprey: ra ee) ON aS Rie G, meh en\ni RS ba wi.\nye as Die Wg ad i} Meas» ao :\nPye eB Pay yy Na ‘ Ops Ry ts ye ‘\n7 CE pl ads a ga 7a\n(on i a fy he \"f NAD ey\nBe z pf he hy pe a bai, Bt ah te\nBP El Rg 5 ee\n+, sya foe (et ol Male atl fey £\n. ar ALAS i HpeAids ‘LG a\nJee Uy di Ae 5 a a a\nae mete! aks Pan ld PAG ae x BEL a’)\n: gael wag AL esa) at whine sf\nPa oe Ea eae spy\nSO Lyaer eel oe\nrad Rh eh Lavin RON avs ie te\nS Sa nas) (OS oan\nBe a Cyn ey Hone DER he Pa *\nOs, doe a ON RS Oa ae .\nOEE OE A UE re ae De Sn\nMET AUL RRP ya >\n", "error_message": "", "processed_image": "dataset_results\\processed_6.jpg"}, {"filename": "7.jpg", "filepath": "dataset\\7.jpg", "process_time": "2025-07-04 11:36:49", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "eR a 18 GEE  *\nnn ie Le\nPatt A OS ma po dete 7\n. 4 “ok we s 4% Geer a\n. rie ar ee as\nwn He es ghee a alag we F F\nre 2 Pid a fit ae fae\nPY a ee EE go J\nA ee a ee at\nea ee Ot © oy a\ntoe a a oe eves\nuo Cf) j\n5 We : 2 ¢ ‘ 4 ue it y . ‘\nee eS\nhoa \\, A ne Nig sal yh\n“hey ae AM Aa th d\npo RE \\\nte veel 4 i : ~\n; yo es rl F : eg sa\neee ee j 4 ) il\nMM ge Po -\nti ye ye Oe i an |\na bal vei , ) :\n. ts A ee P Aor F\nS * rn ina \\ : ‘ 4\n. - eee an i . : 4\nesto rat 3 a 4\n> ean WR Ee EE 4,\ner mis eee tye OS .\neS ade , ™ \"\n; . ee ie ~ s . .\n= , Ree Qh ew oy j aoe o™\n‘ aed ang 4 Fa) . :\npe . AMS. i, . a ee\nys . Seis eee a? ‘\n. a oe ee : E\n, “ JON ben Bg\ns . s d\na % ah . ! ee 4\n: Po; Fr a og\n, ley eg a 42 / 4: f F\nle ei , 4\na Mea oe : 1 Bos b., ee\n¢ : . i\" eae”\na a F Bap | ; ail\nj va : /\nB\n33,\nBD ,\na\n; — —\n| 7 ; ¥ | |\ntea | ail -\nyo *\nP ota f —\nae, ’ .\n¢ f 4 \"Re :\n~ J a ate:\nwae ‘\npee eee ‘ee j cecilia\nEe “Ue ’ % il i\n‘ ig ae ins . iN % ¥ E\nVANS paai' 7 or ',\nq m po mam oF , a\n", "error_message": "", "processed_image": "dataset_results\\processed_7.jpg"}, {"filename": "9.jpg", "filepath": "dataset\\9.jpg", "process_time": "2025-07-04 11:36:52", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "’\n$ we «\n. a\na gy te en ( { € : iy\ncunt re ‘ . \\\newtte, 1% as ave ¥ wore S \\ t cone ‘\n“ay fe? Me\ne emit £7\n; We Lang Ss iene’\ne i rs\ney a V\nre pa’\n. coh NE\n_ ' BRA]\n¢ & &\nte)\niat oo\nj a YY\n: 7 ie A :\n‘ = a ee 4G ae + és\nSe . 9 ti SS mae Pea\n‘ Oy iii ’ we Pera 4 i a ey ‘ ° Bo\n‘% : eet eh es ie ¥. a ie ee\nA F Pee UES Ge we 4 ats \" & . ce ete. ra\ndj ft . ¥ ee ad é i\nFf . Ss 4 ‘2 & . :.\nSS fe geet © : . . says : ce ee\nMeee ely = i, -\nVeg ae ¢ ‘ F 7\nae. fer 7 :\no Sele ' | ;\nis 4 ‘ Bore stent x 7\n‘ he \\-* coo e* (OR\n. eee eee Pr ey es Ot ° j . E - u ec ns\n: i ‘Sn a , f . fa Eh ee ll\nfi ng ae Fe ’ . j a i eo a\nae = . 2 i a me . ee e , ae. : <> ae\nbey fu. _ %  & F = q :\n‘ z bi b\n", "error_message": "", "processed_image": "dataset_results\\processed_9.jpg"}, {"filename": "img.png", "filepath": "dataset\\img.png", "process_time": "2025-07-04 11:36:54", "status": "no_serial_found", "serial_numbers": [], "ocr_text": "« * . x\n~ * .\n®\nae . +\n—~.\n", "error_message": "", "processed_image": "dataset_results\\processed_img.png"}]
@echo off
chcp 65001 >nul
title Dataset文件夹SERIAL数字批量提取工具

echo ========================================
echo Dataset文件夹SERIAL数字批量提取工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python
    echo.
    echo 请先安装Python：
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本的Python
    echo 3. 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Python已安装，版本信息：
python --version
echo.

REM 检查dataset文件夹
if not exist "dataset" (
    echo 错误：当前目录下没有找到 dataset 文件夹
    echo 请确保dataset文件夹存在并包含要处理的图片文件
    echo.
    pause
    exit /b 1
)

echo 检查dataset文件夹内容...
dir dataset\*.jpg dataset\*.png dataset\*.bmp 2>nul | find "个文件" >nul
if errorlevel 1 (
    echo 警告：dataset文件夹中可能没有图片文件
    echo 支持的格式：JPG, PNG, BMP, TIFF, GIF
    echo.
)

REM 检查并安装必要的库
echo 正在检查必要的库...

python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Pillow库...
    pip install Pillow
    if errorlevel 1 (
        echo 警告：Pillow库安装失败，程序可能无法正常运行
    ) else (
        echo Pillow库安装成功
    )
) else (
    echo Pillow库已安装
)

REM 尝试安装OCR库（可选）
echo 正在检查OCR库...
python -c "import pytesseract" >nul 2>&1
if errorlevel 1 (
    echo 正在尝试安装OCR库...
    pip install pytesseract >nul 2>&1
    if errorlevel 1 (
        echo 注意：OCR库安装失败，识别准确率可能较低
    ) else (
        echo OCR库安装成功
    )
) else (
    echo OCR库已安装
)

echo.
echo 准备工作完成，正在启动批量处理...
echo.

REM 运行批量处理程序
if exist "batch_extract_dataset.py" (
    python batch_extract_dataset.py
) else (
    echo 错误：找不到 batch_extract_dataset.py 文件
    echo 请确保所有文件都在同一文件夹中
    pause
    exit /b 1
)

echo.
echo 批量处理完成！
echo 结果文件已保存到 dataset_results 文件夹中
echo.
pause

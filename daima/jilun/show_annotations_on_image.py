#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在原图片上显示标注内容
将标注的SERIAL数字和框显示在原图片上并保存
"""

import cv2
import json
import os
import numpy as np
from datetime import datetime

class AnnotationVisualizer:
    def __init__(self):
        self.annotations = {}
        self.load_annotations()
    
    def load_annotations(self):
        """加载标注数据"""
        annotation_file = "serial_annotations.json"
        if os.path.exists(annotation_file):
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    self.annotations = json.load(f)
                print(f"✅ 已加载标注数据，包含 {len(self.annotations)} 个文件的标注")
            except Exception as e:
                print(f"❌ 加载标注数据失败: {str(e)}")
        else:
            print("❌ 未找到标注数据文件 serial_annotations.json")
    
    def draw_annotations_on_image(self, image_path, output_path=None):
        """在图片上绘制标注"""
        try:
            # 加载图片
            img = cv2.imread(image_path)
            if img is None:
                print(f"❌ 无法加载图片: {image_path}")
                return False
            
            filename = os.path.basename(image_path)
            
            # 检查是否有标注数据
            if filename not in self.annotations:
                print(f"⚠️ 图片 {filename} 没有标注数据")
                return False
            
            boxes = self.annotations[filename]
            if not boxes:
                print(f"⚠️ 图片 {filename} 的标注数据为空")
                return False
            
            print(f"🎯 为图片 {filename} 绘制 {len(boxes)} 个标注")
            
            # 复制图片以避免修改原图
            annotated_img = img.copy()
            
            # 绘制每个标注框
            for i, box in enumerate(boxes):
                x, y, w, h = box['x'], box['y'], box['w'], box['h']
                serial = box['serial']
                
                # 确保坐标在图片范围内
                img_height, img_width = img.shape[:2]
                x = max(0, min(x, img_width - 1))
                y = max(0, min(y, img_height - 1))
                w = min(w, img_width - x)
                h = min(h, img_height - y)
                
                if w <= 0 or h <= 0:
                    print(f"⚠️ 跳过无效标注框 {i+1}: ({x}, {y}, {w}, {h})")
                    continue
                
                # 绘制矩形框
                color = (0, 0, 255)  # 红色 (BGR格式)
                thickness = 3
                cv2.rectangle(annotated_img, (x, y), (x + w, y + h), color, thickness)
                
                # 计算文字大小和位置
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 1.0
                text_thickness = 2
                
                # 获取文字尺寸
                (text_width, text_height), baseline = cv2.getTextSize(serial, font, font_scale, text_thickness)
                
                # 计算文字背景框位置
                text_x = x
                text_y = y - 10 if y - 10 > text_height else y + h + text_height + 10
                
                # 确保文字在图片范围内
                if text_x + text_width > img_width:
                    text_x = img_width - text_width
                if text_y < text_height:
                    text_y = text_height
                if text_y > img_height:
                    text_y = img_height - 5
                
                # 绘制文字背景
                bg_x1 = text_x - 5
                bg_y1 = text_y - text_height - 5
                bg_x2 = text_x + text_width + 5
                bg_y2 = text_y + baseline + 5
                
                cv2.rectangle(annotated_img, (bg_x1, bg_y1), (bg_x2, bg_y2), (255, 255, 255), -1)
                cv2.rectangle(annotated_img, (bg_x1, bg_y1), (bg_x2, bg_y2), color, 2)
                
                # 绘制文字
                cv2.putText(annotated_img, serial, (text_x, text_y), font, font_scale, color, text_thickness)
                
                # 绘制标注序号
                number_text = f"{i+1}"
                number_x = x + 5
                number_y = y + 25
                cv2.putText(annotated_img, number_text, (number_x, number_y), font, 0.7, (255, 255, 0), 2)
                
                print(f"  ✅ 标注 {i+1}: {serial} at ({x}, {y}, {w}, {h})")
            
            # 保存标注后的图片
            if output_path is None:
                base_name = os.path.splitext(filename)[0]
                output_path = f"annotated_{base_name}.jpg"
            
            success = cv2.imwrite(output_path, annotated_img)
            if success:
                print(f"✅ 已保存标注图片: {output_path}")
                return True
            else:
                print(f"❌ 保存图片失败: {output_path}")
                return False
                
        except Exception as e:
            print(f"❌ 处理图片时出错: {str(e)}")
            return False
    
    def process_all_annotated_images(self):
        """处理所有有标注的图片"""
        if not self.annotations:
            print("❌ 没有标注数据可处理")
            return
        
        print(f"🚀 开始处理 {len(self.annotations)} 个标注文件...")
        
        # 创建输出文件夹
        output_folder = "annotated_images"
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            print(f"📁 创建输出文件夹: {output_folder}")
        
        success_count = 0
        total_count = len(self.annotations)
        
        for filename in self.annotations.keys():
            # 查找图片文件
            image_path = None
            for folder in ["dataset", "."]:
                potential_path = os.path.join(folder, filename)
                if os.path.exists(potential_path):
                    image_path = potential_path
                    break
            
            if image_path is None:
                print(f"⚠️ 找不到图片文件: {filename}")
                continue
            
            # 生成输出路径
            base_name = os.path.splitext(filename)[0]
            output_path = os.path.join(output_folder, f"annotated_{base_name}.jpg")
            
            # 处理图片
            if self.draw_annotations_on_image(image_path, output_path):
                success_count += 1
            
            print("-" * 50)
        
        print(f"🎉 处理完成！成功: {success_count}/{total_count}")
        print(f"📁 标注图片保存在: {output_folder} 文件夹")
    
    def process_single_image(self, image_path):
        """处理单个图片"""
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return False
        
        filename = os.path.basename(image_path)
        base_name = os.path.splitext(filename)[0]
        output_path = f"annotated_{base_name}.jpg"
        
        return self.draw_annotations_on_image(image_path, output_path)
    
    def show_annotation_summary(self):
        """显示标注数据摘要"""
        if not self.annotations:
            print("❌ 没有标注数据")
            return
        
        print("📊 标注数据摘要:")
        print("=" * 60)
        
        total_annotations = 0
        for filename, boxes in self.annotations.items():
            count = len(boxes)
            total_annotations += count
            print(f"📄 {filename}: {count} 个标注")
            
            for i, box in enumerate(boxes):
                serial = box['serial']
                x, y, w, h = box['x'], box['y'], box['w'], box['h']
                print(f"   {i+1}. {serial} - 位置: ({x}, {y}) 大小: {w}x{h}")
        
        print("=" * 60)
        print(f"📈 总计: {len(self.annotations)} 个文件, {total_annotations} 个标注")

def main():
    print("🎨 SERIAL标注可视化工具")
    print("=" * 50)
    
    visualizer = AnnotationVisualizer()
    
    if not visualizer.annotations:
        print("请先使用标注工具创建标注数据")
        return
    
    # 显示标注摘要
    visualizer.show_annotation_summary()
    print()
    
    while True:
        print("请选择操作:")
        print("1. 处理所有标注图片")
        print("2. 处理单个图片")
        print("3. 显示标注摘要")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("\n🚀 开始处理所有标注图片...")
            visualizer.process_all_annotated_images()
            
        elif choice == "2":
            image_path = input("请输入图片路径: ").strip()
            if image_path:
                print(f"\n🎯 处理图片: {image_path}")
                visualizer.process_single_image(image_path)
            
        elif choice == "3":
            print()
            visualizer.show_annotation_summary()
            
        elif choice == "4":
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    main()

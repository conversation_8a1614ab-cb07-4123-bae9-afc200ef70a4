@echo off
chcp 65001 >nul
title SERIAL数字标注工具

echo ========================================
echo SERIAL数字标注工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python
    echo.
    echo 请先安装Python：
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本的Python
    echo 3. 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Python已安装，版本信息：
python --version
echo.

REM 检查dataset文件夹
if not exist "dataset" (
    echo 警告：当前目录下没有找到 dataset 文件夹
    echo 标注工具仍可使用，但建议将图片放在dataset文件夹中
    echo.
)

REM 检查并安装必要的库
echo 正在检查必要的库...

python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo 错误：tkinter库未安装，这是Python的标准库
    echo 请重新安装Python并确保包含tkinter
    pause
    exit /b 1
) else (
    echo tkinter库已安装
)

python -c "import cv2" >nul 2>&1
if errorlevel 1 (
    echo 正在安装opencv-python库...
    pip install opencv-python
    if errorlevel 1 (
        echo 错误：opencv-python库安装失败
        pause
        exit /b 1
    ) else (
        echo opencv-python库安装成功
    )
) else (
    echo opencv-python库已安装
)

python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Pillow库...
    pip install Pillow
    if errorlevel 1 (
        echo 错误：Pillow库安装失败
        pause
        exit /b 1
    ) else (
        echo Pillow库安装成功
    )
) else (
    echo Pillow库已安装
)

python -c "import numpy" >nul 2>&1
if errorlevel 1 (
    echo 正在安装numpy库...
    pip install numpy
    if errorlevel 1 (
        echo 错误：numpy库安装失败
        pause
        exit /b 1
    ) else (
        echo numpy库安装成功
    )
) else (
    echo numpy库已安装
)

echo.
echo 所有依赖库检查完成！
echo.
echo 使用说明：
echo 1. 在左侧选择要标注的图片
echo 2. 在右侧图片上用鼠标画框圈出SERIAL数字区域
echo 3. 在输入框中输入对应的SERIAL数字
echo 4. 点击"添加标注"保存标注
echo 5. 完成后点击"保存标注"保存到文件
echo 6. 使用"导出训练数据"生成训练样本
echo.
echo 正在启动标注工具...
echo.

REM 运行标注工具
if exist "serial_annotation_tool.py" (
    python serial_annotation_tool.py
) else (
    echo 错误：找不到 serial_annotation_tool.py 文件
    echo 请确保所有文件都在同一文件夹中
    pause
    exit /b 1
)

echo.
echo 标注工具已关闭
pause

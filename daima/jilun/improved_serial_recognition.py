#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的SERIAL识别工具
1. 先定位SERIAL文字
2. 在其附近区域查找数字
3. 使用多种OCR策略
"""

import cv2
import numpy as np
import os
import json
import pytesseract
import re
from datetime import datetime

class ImprovedSerialRecognizer:
    def __init__(self):
        self.load_annotations()
    
    def load_annotations(self):
        """加载标注数据"""
        try:
            with open("serial_number_annotations.json", 'r', encoding='utf-8') as f:
                self.annotations = json.load(f)
            print(f"✅ 已加载 {len(self.annotations)} 张图片的标注数据")
        except:
            self.annotations = {}
            print("❌ 未找到标注数据")
    
    def load_image(self, image_path):
        """加载图片（处理中文路径）"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            print(f"❌ 加载图片失败: {str(e)}")
            return None
    
    def find_serial_text(self, image):
        """查找SERIAL文字位置"""
        print("🔍 正在查找SERIAL文字...")
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 多种预处理方式
        methods = [
            ("原图", gray),
            ("高斯模糊", cv2.GaussianBlur(gray, (3, 3), 0)),
            ("双边滤波", cv2.bilateralFilter(gray, 9, 75, 75)),
        ]
        
        serial_positions = []
        
        for method_name, processed in methods:
            print(f"   尝试 {method_name}...")
            
            # 二值化
            _, binary = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # OCR查找SERIAL
            try:
                # 使用pytesseract获取详细信息
                data = pytesseract.image_to_data(binary, output_type=pytesseract.Output.DICT)
                
                for i, text in enumerate(data['text']):
                    if text.strip().upper() == 'SERIAL':
                        x = data['left'][i]
                        y = data['top'][i]
                        w = data['width'][i]
                        h = data['height'][i]
                        conf = data['conf'][i]
                        
                        if conf > 30:  # 置信度阈值
                            serial_positions.append({
                                'method': method_name,
                                'x': x, 'y': y, 'w': w, 'h': h,
                                'confidence': conf
                            })
                            print(f"      ✅ 找到SERIAL: 位置({x},{y}) 置信度:{conf}")
            except Exception as e:
                print(f"      ❌ OCR失败: {str(e)}")
        
        return serial_positions
    
    def find_numbers_near_serial(self, image, serial_positions):
        """在SERIAL附近查找数字"""
        print("🔢 在SERIAL附近查找数字...")
        
        results = []
        
        for serial_pos in serial_positions:
            x, y, w, h = serial_pos['x'], serial_pos['y'], serial_pos['w'], serial_pos['h']
            
            # 定义搜索区域（SERIAL右侧）
            search_x = x + w + 10  # SERIAL右侧10像素开始
            search_y = max(0, y - h)  # 上方扩展一个字符高度
            search_w = min(800, image.shape[1] - search_x)  # 向右搜索800像素
            search_h = h * 3  # 高度扩展3倍
            
            # 确保搜索区域在图片范围内
            if search_x >= image.shape[1] or search_y >= image.shape[0]:
                continue
            
            search_h = min(search_h, image.shape[0] - search_y)
            
            print(f"   搜索区域: ({search_x},{search_y}) {search_w}x{search_h}")
            
            # 提取搜索区域
            search_region = image[search_y:search_y+search_h, search_x:search_x+search_w]
            
            # 在搜索区域中查找数字
            numbers = self.extract_numbers_from_region(search_region, search_x, search_y)
            
            for number_info in numbers:
                number_info['serial_method'] = serial_pos['method']
                number_info['serial_confidence'] = serial_pos['confidence']
                results.append(number_info)
        
        return results
    
    def extract_numbers_from_region(self, region, offset_x, offset_y):
        """从区域中提取数字"""
        if region.size == 0:
            return []
        
        # 转换为灰度图
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region
        
        results = []
        
        # 多种处理方法
        processing_methods = [
            ("原图", gray),
            ("OTSU二值化", self.apply_otsu(gray)),
            ("自适应阈值", self.apply_adaptive_threshold(gray)),
            ("形态学处理", self.apply_morphology(gray)),
        ]
        
        for method_name, processed in processing_methods:
            # 放大图像提高OCR效果
            scale_factor = 3
            enlarged = cv2.resize(processed, None, fx=scale_factor, fy=scale_factor, 
                                interpolation=cv2.INTER_CUBIC)
            
            try:
                # OCR配置 - 专门识别数字和字母
                config = '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-'
                text = pytesseract.image_to_string(enlarged, config=config).strip()
                
                # 清理文本
                clean_text = re.sub(r'[^A-Z0-9-]', '', text.upper())
                
                if clean_text and len(clean_text) >= 6:
                    # 验证是否符合SERIAL格式
                    if self.is_valid_serial_format(clean_text):
                        results.append({
                            'text': clean_text,
                            'method': method_name,
                            'region_x': offset_x,
                            'region_y': offset_y,
                            'region_w': region.shape[1],
                            'region_h': region.shape[0],
                            'confidence': 'high'
                        })
                        print(f"      ✅ {method_name}: {clean_text}")
                    else:
                        results.append({
                            'text': clean_text,
                            'method': method_name,
                            'region_x': offset_x,
                            'region_y': offset_y,
                            'region_w': region.shape[1],
                            'region_h': region.shape[0],
                            'confidence': 'low'
                        })
                        print(f"      ❓ {method_name}: {clean_text} (格式不匹配)")
                
            except Exception as e:
                print(f"      ❌ {method_name} OCR失败: {str(e)}")
        
        return results
    
    def apply_otsu(self, gray):
        """OTSU二值化"""
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        return binary
    
    def apply_adaptive_threshold(self, gray):
        """自适应阈值"""
        return cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    
    def apply_morphology(self, gray):
        """形态学处理"""
        # 先二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return processed
    
    def is_valid_serial_format(self, text):
        """验证SERIAL格式"""
        if not text or len(text) < 6:
            return False
        
        # 基于已知格式的模式
        patterns = [
            r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914
            r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
            r'^[A-Z]{3}\d{2}-\d{3,4}$',  # MAR23-1055, SEP23-1153
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                return True
        
        return False
    
    def recognize_image(self, image_path):
        """识别单张图片"""
        print(f"\n{'='*60}")
        print(f"🎯 正在识别: {os.path.basename(image_path)}")
        print(f"{'='*60}")
        
        # 加载图片
        image = self.load_image(image_path)
        if image is None:
            return []
        
        print(f"📐 图片尺寸: {image.shape[1]} x {image.shape[0]}")
        
        # 方法1: 先找SERIAL文字，再找数字
        serial_positions = self.find_serial_text(image)
        
        if serial_positions:
            print(f"✅ 找到 {len(serial_positions)} 个SERIAL文字位置")
            results = self.find_numbers_near_serial(image, serial_positions)
        else:
            print("❌ 未找到SERIAL文字，尝试全图搜索...")
            # 方法2: 全图搜索（备用方案）
            results = self.full_image_search(image)
        
        return results
    
    def full_image_search(self, image):
        """全图搜索备用方案"""
        print("🔍 执行全图搜索...")
        
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 使用OCR获取所有文字
        try:
            data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
            
            results = []
            for i, text in enumerate(data['text']):
                clean_text = re.sub(r'[^A-Z0-9-]', '', text.upper().strip())
                
                if clean_text and len(clean_text) >= 6:
                    if self.is_valid_serial_format(clean_text):
                        x = data['left'][i]
                        y = data['top'][i]
                        w = data['width'][i]
                        h = data['height'][i]
                        conf = data['conf'][i]
                        
                        results.append({
                            'text': clean_text,
                            'method': '全图搜索',
                            'region_x': x,
                            'region_y': y,
                            'region_w': w,
                            'region_h': h,
                            'confidence': 'medium' if conf > 50 else 'low',
                            'ocr_confidence': conf
                        })
                        print(f"   ✅ 找到: {clean_text} (置信度: {conf})")
            
            return results
            
        except Exception as e:
            print(f"❌ 全图搜索失败: {str(e)}")
            return []
    
    def test_single_image(self, image_name):
        """测试单张图片"""
        image_path = os.path.join("ceshi", image_name)
        if not os.path.exists(image_path):
            print(f"❌ 找不到图片: {image_path}")
            return
        
        results = self.recognize_image(image_path)
        
        print(f"\n📊 识别结果汇总:")
        print("-" * 40)
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['text']}")
                print(f"   方法: {result['method']}")
                print(f"   置信度: {result['confidence']}")
                print(f"   位置: ({result['region_x']}, {result['region_y']})")
                print()
        else:
            print("❌ 未识别到任何SERIAL数字")
        
        return results

def main():
    recognizer = ImprovedSerialRecognizer()
    
    print("🎯 改进的SERIAL识别工具")
    print("=" * 50)
    
    # 测试指定图片
    test_image = "微信图片_20250704123039.jpg"
    print(f"测试图片: {test_image}")
    
    results = recognizer.test_single_image(test_image)
    
    # 保存结果
    if results:
        output_file = f"improved_recognition_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({test_image: results}, f, ensure_ascii=False, indent=2)
        print(f"💾 结果已保存到: {output_file}")

if __name__ == "__main__":
    main()

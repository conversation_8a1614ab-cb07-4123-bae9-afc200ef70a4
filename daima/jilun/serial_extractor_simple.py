#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版SERIAL数字提取工具
专门用于从图片中提取SERIAL后面的数字
"""

import cv2
import numpy as np
import re
import os
import sys

def extract_serial_from_image(image_path):
    """从图片中提取SERIAL数字的简化版本"""
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误：图片文件 '{image_path}' 不存在！")
            return []
        
        print(f"正在处理图片: {image_path}")
        
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print("错误：无法读取图片文件")
            return []
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 图像预处理
        # 应用高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 阈值处理
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 尝试使用简单的OCR替代方案
        # 由于可能没有安装tesseract，我们使用图像处理方法
        
        # 查找轮廓
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 创建一个副本用于绘制
        result_img = img.copy()
        
        # 在图片上标记找到的区域
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 过滤太小的区域
            if w > 20 and h > 10:
                cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # 保存处理后的图片
        output_path = "processed_" + os.path.basename(image_path)
        cv2.imwrite(output_path, result_img)
        print(f"处理后的图片已保存为: {output_path}")
        
        # 由于没有OCR，我们提供一个手动输入的提示
        print("\n由于OCR库可能未安装，请手动查看图片中SERIAL后面的数字。")
        print("如果需要自动识别，请安装tesseract-ocr和pytesseract库。")
        
        return []
        
    except Exception as e:
        print(f"处理失败: {str(e)}")
        return []

def main():
    """主函数"""
    print("=" * 50)
    print("SERIAL数字提取工具 - 简化版")
    print("=" * 50)
    
    # 默认处理8.jpg
    image_path = "8.jpg"
    
    # 如果提供了命令行参数，使用指定的图片
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    
    # 检查当前目录下是否有8.jpg
    if not os.path.exists(image_path) and image_path == "8.jpg":
        print("当前目录下没有找到8.jpg文件")
        print("请将图片文件放在与此脚本相同的目录下，或者指定图片路径")
        
        # 让用户输入图片路径
        while True:
            user_input = input("\n请输入图片文件路径（或按Enter退出）: ").strip()
            if not user_input:
                print("退出程序")
                return
            if os.path.exists(user_input):
                image_path = user_input
                break
            else:
                print(f"文件 '{user_input}' 不存在，请重新输入")
    
    # 提取SERIAL数字
    serial_numbers = extract_serial_from_image(image_path)
    
    if serial_numbers:
        print(f"\n找到 {len(serial_numbers)} 个SERIAL数字:")
        for i, serial in enumerate(serial_numbers, 1):
            print(f"{i}. {serial}")
    else:
        print("\n未能自动识别SERIAL数字")
        print("请查看生成的processed_图片文件，手动识别SERIAL后面的数字")
    
    print("\n处理完成！")
    input("按Enter键退出...")

if __name__ == "__main__":
    main()

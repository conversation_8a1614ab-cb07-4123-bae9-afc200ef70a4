SERIAL数字标注工具使用指南
================================

工具简介：
---------
这是一个专门用于标注dataset文件夹中SERIAL后面数字的标注工具。
通过人工标注，我们可以收集高质量的训练数据，用于训练数字识别模型。

启动方法：
---------
双击 "启动SERIAL数字标注工具.bat" 文件

界面说明：
---------

左侧控制面板：
1. 图片选择区域
   - 图片下拉框：选择要标注的图片
   - 路径显示：显示当前图片的完整路径

2. 图片旋转区域
   - ↺ 逆时针90°：将图片逆时针旋转90度
   - ↻ 顺时针90°：将图片顺时针旋转90度
   - 🔄 重置：恢复图片到原始角度
   - 当前角度显示：显示当前旋转角度

3. 标注信息区域
   - SERIAL数字输入框：输入识别出的数字
   - 添加标注按钮：保存当前标注

4. 操作区域
   - 保存标注：手动保存所有标注数据（⭐训练最重要）
   - 导出训练数据：生成训练用的图片切片（⭐训练重要）
   - 保存标注图片：生成带标注框的图片（用于检查质量）
   - 清除当前标注：删除当前图片的所有标注

5. 当前图片标注列表
   - 显示当前图片的所有标注
   - 可选择并删除特定标注

右侧图片显示区域：
- 显示当前选中的图片
- 支持鼠标拖拽画框标注
- 实时显示已有的标注框和内容

使用步骤：
---------

1. 选择图片
   - 在左侧下拉框中选择要标注的图片
   - 图片会显示在右侧区域

2. 调整图片角度（如需要）
   - 如果图片角度不正，可以使用旋转功能
   - 点击"↺ 逆时针90°"或"↻ 顺时针90°"调整角度
   - 调整到SERIAL数字清晰可见的角度
   - 如需恢复原始角度，点击"🔄 重置"

3. 画框标注
   - 在右侧图片上，用鼠标拖拽画框
   - 框选SERIAL后面的数字部分
   - 尽量紧贴数字，不要包含太多背景

4. 输入SERIAL数字
   - 在左侧"SERIAL数字"输入框中输入识别出的数字
   - 只输入数字部分，不要包含"SERIAL"文字
   - 例如：如果看到"SERIAL JAN23-7914"，只输入"JAN23-7914"

5. 添加标注
   - 点击"添加标注"按钮保存当前标注
   - 标注会自动保存到文件中

6. 重复标注
   - 如果一张图片有多个SERIAL数字，重复步骤3-5
   - 每个SERIAL数字都需要单独标注

7. 切换图片
   - 完成当前图片后，选择下一张图片继续标注

8. 导出训练数据
   - 完成所有标注后，点击"导出训练数据"
   - 会生成serial_training_data文件夹，包含所有标注区域的图片

标注技巧：
---------

1. 图片角度调整
   - 如果SERIAL数字是倾斜或倒置的，先旋转图片到合适角度
   - 旋转后的标注会自动转换为原图坐标，无需担心坐标问题
   - 建议将SERIAL数字调整到水平方向，便于阅读和标注

2. 框选范围
   - 尽量紧贴SERIAL数字，不要包含太多背景
   - 确保数字完整，不要遗漏任何字符
   - 框选时要包含完整的数字序列

3. 输入格式
   - 只输入SERIAL后面的数字/字母组合
   - 保持原有的格式，包括连字符等
   - 例如：JAN23-7914、JUL11-0087等

4. 质量控制
   - 仔细检查输入的数字是否正确
   - 如果不确定某个字符，可以放大查看
   - 标注错误可以通过列表删除重新标注

数据文件说明：
-----------

1. serial_number_annotations.json
   - 存储所有标注数据的主文件
   - 包含每张图片的标注框坐标和对应的数字

2. serial_training_data文件夹
   - 导出的训练数据
   - 包含所有标注区域的切片图片
   - training_info.json包含训练数据的详细信息

3. annotated_images文件夹
   - 存放带标注框的图片
   - 文件命名：annotated_原图名.jpg
   - 用于检查标注质量

注意事项：
---------

1. 标注数据会自动保存，无需担心丢失
2. 支持中途退出，下次启动会继续之前的标注
3. 建议定期导出训练数据作为备份
4. 如果发现标注错误，可以删除重新标注
5. 旋转功能不会影响最终的标注坐标

常见问题：
---------

Q: 画框时提示"标注框太小"？
A: 请画更大的框，确保框的宽度和高度都大于10像素

Q: 画框时提示"超出图片范围"？
A: 请确保标注框完全在图片范围内

Q: 如何删除错误的标注？
A: 在左侧标注列表中选择要删除的标注，点击"删除选中标注"

Q: 旋转后标注坐标会错误吗？
A: 不会，工具会自动处理坐标转换，确保标注数据基于原图

Q: 可以标注多个SERIAL数字吗？
A: 可以，一张图片可以标注多个SERIAL数字，每个都需要单独画框和输入

技术支持：
---------
如有问题，请检查：
1. Python是否正确安装
2. 必要的库是否安装完整
3. dataset文件夹是否存在且包含图片
4. 图片格式是否支持（jpg, jpeg, png, bmp）

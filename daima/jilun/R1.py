import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageTk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import re
import os

class SerialNumberExtractor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SERIAL数字提取工具")
        self.root.geometry("800x600")

        # 设置Tesseract路径（如果需要）
        # pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="图片选择", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.file_path_var = tk.StringVar(value="8.jpg")
        ttk.Label(file_frame, text="图片路径:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).grid(row=0, column=1, padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)

        # 处理按钮
        ttk.Button(main_frame, text="提取SERIAL数字", command=self.extract_serial).grid(row=1, column=0, columnspan=2, pady=10)

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="提取结果", padding="5")
        result_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 结果文本框
        self.result_text = tk.Text(result_frame, height=8, width=70)
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 图片预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="图片预览", padding="5")
        preview_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.image_label = ttk.Label(preview_frame)
        self.image_label.grid(row=0, column=0)

        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(3, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_path_var.set(filename)
            self.preview_image(filename)

    def preview_image(self, image_path):
        """预览图片"""
        try:
            # 加载图片
            image = Image.open(image_path)

            # 调整图片大小以适应预览
            max_size = (400, 300)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(image)

            # 显示图片
            self.image_label.configure(image=photo)
            self.image_label.image = photo  # 保持引用

        except Exception as e:
            messagebox.showerror("错误", f"无法预览图片: {str(e)}")

    def preprocess_image(self, image_path):
        """图像预处理以提高OCR识别率"""
        try:
            # 读取图片
            img = cv2.imread(image_path)
            if img is None:
                raise ValueError("无法读取图片文件")

            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 应用高斯模糊去噪
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # 应用阈值处理
            _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 形态学操作去除噪点
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

            return cleaned

        except Exception as e:
            raise Exception(f"图像预处理失败: {str(e)}")

    def extract_text_from_image(self, processed_image):
        """从预处理后的图像中提取文字"""
        try:
            # 配置Tesseract参数
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz '

            # 使用Tesseract进行OCR
            text = pytesseract.image_to_string(processed_image, config=custom_config)

            return text

        except Exception as e:
            raise Exception(f"OCR识别失败: {str(e)}")

    def find_serial_numbers(self, text):
        """从文本中查找SERIAL后面的数字"""
        serial_numbers = []

        # 将文本按行分割
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 查找包含SERIAL的行
            if 'SERIAL' in line.upper():
                # 使用正则表达式查找SERIAL后面的数字
                patterns = [
                    r'SERIAL\s*[:\-]?\s*([A-Z0-9]+)',  # SERIAL: 或 SERIAL- 或 SERIAL 后跟数字字母
                    r'SERIAL\s+([A-Z0-9]+)',           # SERIAL 空格 数字字母
                    r'SERIAL([A-Z0-9]+)',              # SERIAL直接跟数字字母
                ]

                for pattern in patterns:
                    matches = re.findall(pattern, line.upper())
                    for match in matches:
                        if match and len(match) > 0:
                            serial_numbers.append(match)

                # 如果没有找到匹配，尝试提取行中的所有数字字母组合
                if not serial_numbers:
                    # 查找行中所有可能的序列号（数字字母组合）
                    potential_serials = re.findall(r'[A-Z0-9]{3,}', line.upper())
                    for serial in potential_serials:
                        if serial != 'SERIAL':  # 排除SERIAL本身
                            serial_numbers.append(serial)

        return serial_numbers

    def extract_serial(self):
        """主要的提取函数"""
        try:
            image_path = self.file_path_var.get()

            # 检查文件是否存在
            if not os.path.exists(image_path):
                messagebox.showerror("错误", "图片文件不存在！")
                return

            # 清空结果显示
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "正在处理图片...\n")
            self.root.update()

            # 预览图片
            self.preview_image(image_path)

            # 图像预处理
            self.result_text.insert(tk.END, "正在进行图像预处理...\n")
            self.root.update()
            processed_image = self.preprocess_image(image_path)

            # OCR文字识别
            self.result_text.insert(tk.END, "正在进行OCR文字识别...\n")
            self.root.update()
            extracted_text = self.extract_text_from_image(processed_image)

            # 查找SERIAL数字
            self.result_text.insert(tk.END, "正在查找SERIAL数字...\n")
            self.root.update()
            serial_numbers = self.find_serial_numbers(extracted_text)

            # 显示结果
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "=== 提取结果 ===\n\n")

            if serial_numbers:
                self.result_text.insert(tk.END, f"找到 {len(serial_numbers)} 个SERIAL数字:\n")
                for i, serial in enumerate(serial_numbers, 1):
                    self.result_text.insert(tk.END, f"{i}. {serial}\n")
            else:
                self.result_text.insert(tk.END, "未找到SERIAL数字\n")

            self.result_text.insert(tk.END, "\n=== 完整识别文本 ===\n")
            self.result_text.insert(tk.END, extracted_text)

        except Exception as e:
            messagebox.showerror("错误", f"处理失败: {str(e)}")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"错误: {str(e)}")

    def run(self):
        """运行应用程序"""
        # 如果8.jpg存在，自动预览
        if os.path.exists("8.jpg"):
            self.preview_image("8.jpg")

        self.root.mainloop()

if __name__ == "__main__":
    app = SerialNumberExtractor()
    app.run()
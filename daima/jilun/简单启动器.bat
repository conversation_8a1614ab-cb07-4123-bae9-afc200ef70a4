@echo off
chcp 65001 >nul
title 8.jpg SERIAL数字提取工具

echo ========================================
echo 8.jpg SERIAL数字提取工具 - 简化版
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python
    echo.
    echo 请先安装Python：
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本的Python
    echo 3. 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Python已安装，版本信息：
python --version
echo.

REM 检查8.jpg文件
if not exist "8.jpg" (
    echo 警告：当前目录下没有找到 8.jpg 文件
    echo 请将要处理的图片重命名为 8.jpg 并放在此文件夹中
    echo.
    pause
)

REM 检查并安装Pillow库
echo 正在检查图像处理库...
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Pillow库...
    pip install Pillow
    if errorlevel 1 (
        echo 警告：Pillow库安装失败，程序可能无法正常运行
    ) else (
        echo Pillow库安装成功
    )
) else (
    echo Pillow库已安装
)

REM 尝试安装OCR库（可选）
echo 正在检查OCR库...
python -c "import pytesseract" >nul 2>&1
if errorlevel 1 (
    echo 正在尝试安装OCR库...
    pip install pytesseract >nul 2>&1
    if errorlevel 1 (
        echo 注意：OCR库安装失败，将使用手动识别模式
    ) else (
        echo OCR库安装成功
    )
) else (
    echo OCR库已安装
)

echo.
echo 准备工作完成，正在启动程序...
echo.

REM 运行程序
if exist "extract_8jpg.py" (
    python extract_8jpg.py
) else (
    echo 错误：找不到 extract_8jpg.py 文件
    echo 请确保所有文件都在同一文件夹中
    pause
    exit /b 1
)

echo.
pause

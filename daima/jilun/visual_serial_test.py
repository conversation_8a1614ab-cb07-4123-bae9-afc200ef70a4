#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化SERIAL识别测试工具
显示图片处理过程，帮助调试识别算法
"""

import cv2
import numpy as np
import os
import json
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import pytesseract
import re

class VisualSerialTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SERIAL识别可视化测试")
        self.root.geometry("1400x800")
        
        self.current_image = None
        self.processed_image = None
        self.current_image_path = ""
        
        self.load_annotations()
        self.setup_ui()
        self.load_test_images()
    
    def load_annotations(self):
        """加载标注数据"""
        try:
            with open("serial_number_annotations.json", 'r', encoding='utf-8') as f:
                self.annotations = json.load(f)
            print(f"✅ 已加载 {len(self.annotations)} 张图片的标注数据")
        except:
            self.annotations = {}
            print("❌ 未找到标注数据")
    
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 图片选择
        ttk.Label(control_frame, text="测试图片:").pack(anchor=tk.W)
        self.image_var = tk.StringVar()
        self.image_combo = ttk.Combobox(control_frame, textvariable=self.image_var, 
                                       state="readonly", width=25)
        self.image_combo.pack(fill=tk.X, pady=(5, 10))
        self.image_combo.bind('<<ComboboxSelected>>', self.on_image_selected)
        
        # 处理参数
        params_frame = ttk.LabelFrame(control_frame, text="处理参数", padding=5)
        params_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 二值化阈值
        ttk.Label(params_frame, text="二值化阈值:").pack(anchor=tk.W)
        self.threshold_var = tk.IntVar(value=11)
        threshold_scale = ttk.Scale(params_frame, from_=3, to=21, orient=tk.HORIZONTAL,
                                  variable=self.threshold_var, command=self.update_processing)
        threshold_scale.pack(fill=tk.X)
        
        # 最小区域宽度
        ttk.Label(params_frame, text="最小宽度:").pack(anchor=tk.W)
        self.min_width_var = tk.IntVar(value=200)
        width_scale = ttk.Scale(params_frame, from_=50, to=500, orient=tk.HORIZONTAL,
                               variable=self.min_width_var, command=self.update_processing)
        width_scale.pack(fill=tk.X)
        
        # 最小区域高度
        ttk.Label(params_frame, text="最小高度:").pack(anchor=tk.W)
        self.min_height_var = tk.IntVar(value=80)
        height_scale = ttk.Scale(params_frame, from_=20, to=200, orient=tk.HORIZONTAL,
                                variable=self.min_height_var, command=self.update_processing)
        height_scale.pack(fill=tk.X)
        
        # 操作按钮
        ttk.Button(control_frame, text="🔍 重新处理", command=self.process_image).pack(fill=tk.X, pady=5)
        ttk.Button(control_frame, text="📊 OCR识别", command=self.run_ocr).pack(fill=tk.X, pady=5)
        ttk.Button(control_frame, text="💾 保存结果", command=self.save_result).pack(fill=tk.X, pady=5)
        
        # 结果显示
        result_frame = ttk.LabelFrame(control_frame, text="识别结果", padding=5)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.result_text = tk.Text(result_frame, height=15, width=30, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧图片显示区域
        image_frame = ttk.Frame(main_frame)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 原图显示
        original_frame = ttk.LabelFrame(image_frame, text="原图", padding=5)
        original_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.original_label = ttk.Label(original_frame)
        self.original_label.pack(expand=True)
        
        # 处理后图片显示
        processed_frame = ttk.LabelFrame(image_frame, text="处理后 + 检测区域", padding=5)
        processed_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        self.processed_label = ttk.Label(processed_frame)
        self.processed_label.pack(expand=True)
    
    def load_test_images(self):
        """加载测试图片"""
        ceshi_folder = "ceshi"
        if not os.path.exists(ceshi_folder):
            messagebox.showerror("错误", "找不到ceshi文件夹")
            return
        
        image_files = []
        for file in os.listdir(ceshi_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)
        
        if not image_files:
            messagebox.showwarning("警告", "ceshi文件夹中没有图片")
            return
        
        self.image_combo['values'] = image_files
        if image_files:
            self.image_combo.current(0)
            self.on_image_selected(None)
    
    def on_image_selected(self, event):
        """图片选择事件"""
        selected = self.image_var.get()
        if not selected:
            return
        
        self.current_image_path = os.path.join("ceshi", selected)
        self.load_current_image()
        self.process_image()
    
    def load_current_image(self):
        """加载当前图片"""
        try:
            # 使用cv2.imdecode处理中文路径
            with open(self.current_image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            self.current_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if self.current_image is None:
                messagebox.showerror("错误", "无法加载图片")
                return
            
            # 显示原图
            self.display_original()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")
    
    def display_original(self):
        """显示原图"""
        if self.current_image is None:
            return
        
        # 转换颜色空间
        rgb_image = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
        
        # 缩放图片适应显示
        display_image = self.resize_for_display(rgb_image, 500, 300)
        
        # 转换为PIL格式
        pil_image = Image.fromarray(display_image)
        photo = ImageTk.PhotoImage(pil_image)
        
        self.original_label.configure(image=photo)
        self.original_label.image = photo
    
    def resize_for_display(self, image, max_width, max_height):
        """缩放图片用于显示"""
        h, w = image.shape[:2]
        
        # 计算缩放比例
        scale_w = max_width / w
        scale_h = max_height / h
        scale = min(scale_w, scale_h, 1.0)  # 不放大
        
        if scale < 1.0:
            new_w = int(w * scale)
            new_h = int(h * scale)
            return cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        else:
            return image
    
    def process_image(self):
        """处理图片"""
        if self.current_image is None:
            return
        
        # 转换为灰度图
        gray = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 自适应阈值
        threshold_val = self.threshold_var.get()
        if threshold_val % 2 == 0:  # 确保是奇数
            threshold_val += 1
        
        binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, threshold_val, 2)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 创建结果图片
        result_image = cv2.cvtColor(binary, cv2.COLOR_GRAY2RGB)
        
        # 筛选和绘制候选区域
        min_width = self.min_width_var.get()
        min_height = self.min_height_var.get()
        
        self.candidate_regions = []
        
        for i, contour in enumerate(contours):
            x, y, w, h = cv2.boundingRect(contour)
            
            # 尺寸筛选
            if w >= min_width and h >= min_height:
                # 宽高比筛选
                aspect_ratio = w / h
                if 1.5 < aspect_ratio < 8.0:
                    self.candidate_regions.append((x, y, w, h))
                    
                    # 绘制绿色框
                    cv2.rectangle(result_image, (x, y), (x+w, y+h), (0, 255, 0), 2)
                    cv2.putText(result_image, f"{i+1}", (x, y-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        self.processed_image = result_image
        
        # 显示处理后的图片
        self.display_processed()
        
        # 更新结果文本
        self.update_result_text()
    
    def display_processed(self):
        """显示处理后的图片"""
        if self.processed_image is None:
            return
        
        # 缩放图片适应显示
        display_image = self.resize_for_display(self.processed_image, 500, 300)
        
        # 转换为PIL格式
        pil_image = Image.fromarray(display_image)
        photo = ImageTk.PhotoImage(pil_image)
        
        self.processed_label.configure(image=photo)
        self.processed_label.image = photo
    
    def update_result_text(self):
        """更新结果文本"""
        self.result_text.delete(1.0, tk.END)
        
        filename = os.path.basename(self.current_image_path)
        self.result_text.insert(tk.END, f"📄 {filename}\n")
        self.result_text.insert(tk.END, f"📐 尺寸: {self.current_image.shape[1]}x{self.current_image.shape[0]}\n")
        self.result_text.insert(tk.END, f"🔍 找到 {len(self.candidate_regions)} 个候选区域\n\n")
        
        for i, (x, y, w, h) in enumerate(self.candidate_regions, 1):
            self.result_text.insert(tk.END, f"区域 {i}:\n")
            self.result_text.insert(tk.END, f"  位置: ({x}, {y})\n")
            self.result_text.insert(tk.END, f"  大小: {w} x {h}\n")
            self.result_text.insert(tk.END, f"  宽高比: {w/h:.2f}\n\n")
    
    def update_processing(self, value):
        """参数变化时更新处理"""
        if self.current_image is not None:
            self.process_image()
    
    def run_ocr(self):
        """运行OCR识别"""
        if self.current_image is None or not self.candidate_regions:
            messagebox.showwarning("警告", "请先选择图片并处理")
            return
        
        self.result_text.insert(tk.END, "\n" + "="*30 + "\n")
        self.result_text.insert(tk.END, "🔤 OCR识别结果:\n")
        self.result_text.insert(tk.END, "="*30 + "\n")
        
        for i, (x, y, w, h) in enumerate(self.candidate_regions, 1):
            # 提取区域
            roi = self.current_image[y:y+h, x:x+w]
            
            # 预处理ROI
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            
            # 放大
            scale_factor = 3
            enlarged = cv2.resize(gray_roi, None, fx=scale_factor, fy=scale_factor, 
                                interpolation=cv2.INTER_CUBIC)
            
            try:
                # OCR
                config = '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-'
                text = pytesseract.image_to_string(enlarged, config=config).strip()
                
                # 清理文本
                clean_text = re.sub(r'[^A-Z0-9-]', '', text.upper())
                
                self.result_text.insert(tk.END, f"区域 {i}: '{clean_text}'\n")
                
                # 验证格式
                if self.is_valid_serial(clean_text):
                    self.result_text.insert(tk.END, f"  ✅ 有效SERIAL\n")
                else:
                    self.result_text.insert(tk.END, f"  ❓ 格式不匹配\n")
                
            except Exception as e:
                self.result_text.insert(tk.END, f"区域 {i}: OCR失败 - {str(e)}\n")
        
        self.result_text.see(tk.END)
    
    def is_valid_serial(self, text):
        """验证SERIAL格式"""
        if not text or len(text) < 6:
            return False
        
        patterns = [
            r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914
            r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
            r'^[A-Z]{3}\d{2}-\d{3,4}$',  # MAR23-1055
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                return True
        
        return False
    
    def save_result(self):
        """保存当前结果"""
        if not self.current_image_path:
            return
        
        filename = os.path.basename(self.current_image_path)
        result_text = self.result_text.get(1.0, tk.END)
        
        with open(f"visual_test_result_{filename}.txt", 'w', encoding='utf-8') as f:
            f.write(result_text)
        
        messagebox.showinfo("成功", f"结果已保存到 visual_test_result_{filename}.txt")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    app = VisualSerialTest()
    app.run()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动SERIAL输入工具
既然OCR识别困难，我们创建一个快速的手动输入工具
您看到什么就输入什么，程序帮您验证和保存
"""

import tkinter as tk
from tkinter import ttk, messagebox
import cv2
import numpy as np
import os
import json
import re
from datetime import datetime
from PIL import Image, ImageTk

class ManualSerialInputTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("手动SERIAL输入工具")
        self.root.geometry("1000x700")
        
        self.current_image = None
        self.current_image_path = ""
        self.results = {}
        
        self.known_serials = [
            "JAN23-7914", "JUL11-0087", "JAN23-7913", 
            "SEP23-1153", "FE823-1036", "MAR23-1055", "JUN23-1105"
        ]
        
        self.setup_ui()
        self.load_images()
    
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="SERIAL输入", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 图片选择
        ttk.Label(control_frame, text="当前图片:").pack(anchor=tk.W)
        self.image_var = tk.StringVar()
        self.image_combo = ttk.Combobox(control_frame, textvariable=self.image_var, 
                                       state="readonly", width=30)
        self.image_combo.pack(fill=tk.X, pady=(5, 10))
        self.image_combo.bind('<<ComboboxSelected>>', self.on_image_selected)
        
        # 导航按钮
        nav_frame = ttk.Frame(control_frame)
        nav_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(nav_frame, text="⬅️ 上一张", command=self.prev_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="➡️ 下一张", command=self.next_image).pack(side=tk.LEFT)
        
        # SERIAL输入
        input_frame = ttk.LabelFrame(control_frame, text="输入SERIAL", padding=10)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_frame, text="您看到的SERIAL:").pack(anchor=tk.W)
        self.serial_var = tk.StringVar()
        self.serial_entry = ttk.Entry(input_frame, textvariable=self.serial_var, 
                                     font=("Courier", 12), width=25)
        self.serial_entry.pack(fill=tk.X, pady=(5, 10))
        self.serial_entry.bind('<Return>', self.validate_and_save)
        self.serial_entry.bind('<KeyRelease>', self.on_serial_change)
        
        # 验证结果显示
        self.validation_label = ttk.Label(input_frame, text="", foreground="blue")
        self.validation_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 操作按钮
        ttk.Button(input_frame, text="✅ 保存SERIAL", 
                  command=self.validate_and_save).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(input_frame, text="❌ 无SERIAL", 
                  command=self.mark_no_serial).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(input_frame, text="🔄 清除", 
                  command=self.clear_input).pack(fill=tk.X)
        
        # 已知SERIAL参考
        ref_frame = ttk.LabelFrame(control_frame, text="已知SERIAL参考", padding=5)
        ref_frame.pack(fill=tk.X, pady=(10, 0))
        
        ref_text = tk.Text(ref_frame, height=8, width=25, wrap=tk.WORD, font=("Courier", 9))
        ref_scrollbar = ttk.Scrollbar(ref_frame, orient=tk.VERTICAL, command=ref_text.yview)
        ref_text.configure(yscrollcommand=ref_scrollbar.set)
        
        ref_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        ref_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 填充已知SERIAL
        for i, serial in enumerate(self.known_serials, 1):
            ref_text.insert(tk.END, f"{i}. {serial}\n")
        ref_text.configure(state=tk.DISABLED)
        
        # 进度和结果
        progress_frame = ttk.LabelFrame(control_frame, text="进度", padding=5)
        progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.progress_label = ttk.Label(progress_frame, text="0/0 已完成")
        self.progress_label.pack()
        
        # 右侧图片显示
        image_frame = ttk.LabelFrame(main_frame, text="图片显示", padding=5)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.image_label = ttk.Label(image_frame)
        self.image_label.pack(expand=True)
        
        # 底部结果显示
        result_frame = ttk.LabelFrame(self.root, text="识别结果", padding=5)
        result_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.result_text = tk.Text(result_frame, height=6, wrap=tk.WORD)
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 底部按钮
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="💾 导出结果", command=self.export_results).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="📊 查看统计", command=self.show_statistics).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🔄 重置所有", command=self.reset_all).pack(side=tk.LEFT)
    
    def load_images(self):
        """加载图片列表"""
        ceshi_folder = "ceshi"
        if not os.path.exists(ceshi_folder):
            messagebox.showerror("错误", "找不到ceshi文件夹")
            return
        
        image_files = []
        for file in os.listdir(ceshi_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)
        
        if not image_files:
            messagebox.showwarning("警告", "ceshi文件夹中没有图片")
            return
        
        self.image_combo['values'] = image_files
        if image_files:
            self.image_combo.current(0)
            self.on_image_selected(None)
    
    def on_image_selected(self, event):
        """图片选择事件"""
        selected = self.image_var.get()
        if not selected:
            return
        
        self.current_image_path = os.path.join("ceshi", selected)
        self.load_current_image()
        self.update_input_for_current_image()
        self.update_progress()
    
    def load_current_image(self):
        """加载当前图片"""
        try:
            with open(self.current_image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            self.current_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if self.current_image is None:
                messagebox.showerror("错误", "无法加载图片")
                return
            
            # 显示图片
            self.display_image()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")
    
    def display_image(self):
        """显示图片"""
        if self.current_image is None:
            return
        
        # 转换颜色空间
        rgb_image = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
        
        # 缩放图片适应显示
        display_image = self.resize_for_display(rgb_image, 600, 400)
        
        # 转换为PIL格式
        pil_image = Image.fromarray(display_image)
        photo = ImageTk.PhotoImage(pil_image)
        
        self.image_label.configure(image=photo)
        self.image_label.image = photo
    
    def resize_for_display(self, image, max_width, max_height):
        """缩放图片用于显示"""
        h, w = image.shape[:2]
        
        scale_w = max_width / w
        scale_h = max_height / h
        scale = min(scale_w, scale_h, 1.0)
        
        if scale < 1.0:
            new_w = int(w * scale)
            new_h = int(h * scale)
            return cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
        else:
            return image
    
    def update_input_for_current_image(self):
        """更新当前图片的输入"""
        filename = os.path.basename(self.current_image_path)
        
        if filename in self.results:
            result = self.results[filename]
            if result['serial']:
                self.serial_var.set(result['serial'])
                self.validation_label.configure(text="✅ 已保存", foreground="green")
            else:
                self.serial_var.set("")
                self.validation_label.configure(text="❌ 标记为无SERIAL", foreground="red")
        else:
            self.serial_var.set("")
            self.validation_label.configure(text="⏳ 待输入", foreground="blue")
        
        # 如果是目标图片，提示正确答案
        if filename == "微信图片_20250704123039.jpg":
            self.result_text.insert(tk.END, f"💡 提示: 这张图片的SERIAL是 JUN23-1105\n")
            self.result_text.see(tk.END)
    
    def on_serial_change(self, event):
        """SERIAL输入变化事件"""
        serial = self.serial_var.get().upper().strip()
        
        if not serial:
            self.validation_label.configure(text="⏳ 请输入SERIAL", foreground="blue")
            return
        
        # 实时验证
        is_valid, message = self.validate_serial_format(serial)
        
        if is_valid:
            self.validation_label.configure(text=f"✅ {message}", foreground="green")
        else:
            self.validation_label.configure(text=f"❓ {message}", foreground="orange")
    
    def validate_serial_format(self, serial):
        """验证SERIAL格式"""
        if not serial:
            return False, "空输入"
        
        # 检查长度
        if len(serial) < 6:
            return False, "太短"
        
        if len(serial) > 15:
            return False, "太长"
        
        # 检查格式
        patterns = [
            (r'^[A-Z]{3}\d{2}-\d{4}$', "标准格式 (如JAN23-7914)"),
            (r'^[A-Z]{2}\d{3}-\d{4}$', "变体格式 (如FE823-1036)"),
            (r'^[A-Z]{3}\d{2}-\d{3}$', "短编号格式"),
        ]
        
        for pattern, desc in patterns:
            if re.match(pattern, serial):
                return True, desc
        
        # 检查是否包含基本元素
        has_letters = bool(re.search(r'[A-Z]', serial))
        has_numbers = bool(re.search(r'\d', serial))
        has_dash = '-' in serial
        
        if has_letters and has_numbers and has_dash:
            return True, "可能的SERIAL格式"
        else:
            missing = []
            if not has_letters: missing.append("字母")
            if not has_numbers: missing.append("数字")
            if not has_dash: missing.append("连字符")
            return False, f"缺少: {', '.join(missing)}"
    
    def validate_and_save(self, event=None):
        """验证并保存SERIAL"""
        serial = self.serial_var.get().upper().strip()
        filename = os.path.basename(self.current_image_path)
        
        if not serial:
            messagebox.showwarning("警告", "请输入SERIAL")
            return
        
        is_valid, message = self.validate_serial_format(serial)
        
        # 保存结果
        self.results[filename] = {
            'serial': serial,
            'valid': is_valid,
            'message': message,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 更新显示
        status = "✅" if is_valid else "❓"
        self.result_text.insert(tk.END, f"{status} {filename}: {serial} ({message})\n")
        self.result_text.see(tk.END)
        
        self.validation_label.configure(text=f"✅ 已保存: {message}", foreground="green")
        self.update_progress()
        
        # 自动跳转到下一张
        self.next_image()
    
    def mark_no_serial(self):
        """标记无SERIAL"""
        filename = os.path.basename(self.current_image_path)
        
        self.results[filename] = {
            'serial': None,
            'valid': False,
            'message': "无SERIAL",
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        self.serial_var.set("")
        self.validation_label.configure(text="❌ 标记为无SERIAL", foreground="red")
        
        self.result_text.insert(tk.END, f"❌ {filename}: 无SERIAL\n")
        self.result_text.see(tk.END)
        
        self.update_progress()
        self.next_image()
    
    def clear_input(self):
        """清除输入"""
        self.serial_var.set("")
        self.validation_label.configure(text="⏳ 待输入", foreground="blue")
    
    def prev_image(self):
        """上一张图片"""
        current_index = self.image_combo.current()
        if current_index > 0:
            self.image_combo.current(current_index - 1)
            self.on_image_selected(None)
    
    def next_image(self):
        """下一张图片"""
        current_index = self.image_combo.current()
        total_count = len(self.image_combo['values'])
        if current_index < total_count - 1:
            self.image_combo.current(current_index + 1)
            self.on_image_selected(None)
    
    def update_progress(self):
        """更新进度"""
        total = len(self.image_combo['values'])
        completed = len(self.results)
        self.progress_label.configure(text=f"{completed}/{total} 已完成")
    
    def export_results(self):
        """导出结果"""
        if not self.results:
            messagebox.showwarning("警告", "没有结果可导出")
            return
        
        output_file = f"manual_serial_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        messagebox.showinfo("成功", f"结果已导出到: {output_file}")
    
    def show_statistics(self):
        """显示统计信息"""
        if not self.results:
            messagebox.showinfo("统计", "暂无数据")
            return
        
        total = len(self.results)
        with_serial = sum(1 for r in self.results.values() if r['serial'])
        without_serial = total - with_serial
        
        valid_serials = [r['serial'] for r in self.results.values() if r['serial'] and r['valid']]
        
        stats = f"""统计信息:
总图片数: {total}
有SERIAL: {with_serial}
无SERIAL: {without_serial}
有效格式: {len(valid_serials)}

识别的SERIAL:
{chr(10).join(valid_serials) if valid_serials else '无'}"""
        
        messagebox.showinfo("统计信息", stats)
    
    def reset_all(self):
        """重置所有结果"""
        if messagebox.askyesno("确认", "确定要重置所有结果吗？"):
            self.results = {}
            self.result_text.delete(1.0, tk.END)
            self.update_progress()
            self.update_input_for_current_image()
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    app = ManualSerialInputTool()
    app.run()

if __name__ == "__main__":
    main()

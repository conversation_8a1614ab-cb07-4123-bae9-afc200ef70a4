#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SERIAL数字标注工具
用于在图片上画框标注SERIAL数字位置，提供训练数据
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import json
import os
import glob
from datetime import datetime

class SerialAnnotationTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SERIAL数字标注工具")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.current_image_path = None
        self.current_image = None
        self.original_image = None  # 保存原始图片
        self.display_image = None
        self.scale_factor = 1.0
        self.rotation_angle = 0  # 当前旋转角度
        self.annotations = {}  # 存储所有标注数据
        self.current_boxes = []  # 当前图片的标注框
        
        # 画框相关
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.current_rect = None
        
        # 图片列表
        self.image_files = []
        self.current_image_index = 0
        
        self.setup_ui()
        self.load_image_list()
        self.load_annotations()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 图片选择区域
        img_frame = ttk.LabelFrame(control_frame, text="图片选择", padding="5")
        img_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.image_var = tk.StringVar()
        self.image_combo = ttk.Combobox(img_frame, textvariable=self.image_var, state="readonly", width=35)
        self.image_combo.pack(fill=tk.X, pady=(0, 5))
        self.image_combo.bind('<<ComboboxSelected>>', self.on_image_selected)
        
        nav_frame = ttk.Frame(img_frame)
        nav_frame.pack(fill=tk.X)
        ttk.Button(nav_frame, text="上一张", command=self.prev_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="下一张", command=self.next_image).pack(side=tk.LEFT)
        ttk.Button(nav_frame, text="浏览文件", command=self.browse_file).pack(side=tk.RIGHT)
        
        # 标注信息区域
        annotation_frame = ttk.LabelFrame(control_frame, text="标注信息", padding="5")
        annotation_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(annotation_frame, text="SERIAL数字:").pack(anchor=tk.W)
        self.serial_entry = ttk.Entry(annotation_frame, width=35)
        self.serial_entry.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(annotation_frame, text="添加标注", command=self.add_annotation).pack(fill=tk.X, pady=(0, 5))
        
        # 当前标注列表
        list_frame = ttk.LabelFrame(control_frame, text="当前图片标注", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.annotation_listbox = tk.Listbox(list_container, height=8)
        scrollbar = ttk.Scrollbar(list_container, orient="vertical", command=self.annotation_listbox.yview)
        self.annotation_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.annotation_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        ttk.Button(list_frame, text="删除选中", command=self.delete_annotation).pack(fill=tk.X, pady=(5, 0))
        
        # 操作按钮区域
        button_frame = ttk.LabelFrame(control_frame, text="操作", padding="5")
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="保存标注", command=self.save_annotations).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="导出训练数据", command=self.export_training_data).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="清除当前标注", command=self.clear_current_annotations).pack(fill=tk.X)
        
        # 右侧图片显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图片标注区域", padding="5")
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建画布
        self.canvas = tk.Canvas(image_frame, bg="white", cursor="crosshair")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.start_draw)
        self.canvas.bind("<B1-Motion>", self.draw_rect)
        self.canvas.bind("<ButtonRelease-1>", self.end_draw)
        
        # 状态栏
        self.status_var = tk.StringVar(value="请选择图片开始标注")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def load_image_list(self):
        """加载图片列表"""
        # 加载dataset文件夹中的图片
        dataset_path = "dataset"
        if os.path.exists(dataset_path):
            extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
            for ext in extensions:
                pattern = os.path.join(dataset_path, ext)
                self.image_files.extend(glob.glob(pattern))
                pattern = os.path.join(dataset_path, ext.upper())
                self.image_files.extend(glob.glob(pattern))
        
        # 去重并排序
        self.image_files = sorted(list(set(self.image_files)))
        
        # 更新下拉框
        if self.image_files:
            filenames = [os.path.basename(f) for f in self.image_files]
            self.image_combo['values'] = filenames
            self.image_combo.current(0)
            self.load_image(self.image_files[0])
        else:
            self.status_var.set("未找到图片文件")
    
    def load_annotations(self):
        """加载已有的标注数据"""
        annotation_file = "serial_annotations.json"
        if os.path.exists(annotation_file):
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    self.annotations = json.load(f)
                self.status_var.set("已加载标注数据")
            except Exception as e:
                self.status_var.set(f"加载标注数据失败: {str(e)}")
    
    def on_image_selected(self, event=None):
        """图片选择事件"""
        selected = self.image_combo.current()
        if selected >= 0:
            self.current_image_index = selected
            self.load_image(self.image_files[selected])
    
    def prev_image(self):
        """上一张图片"""
        if self.image_files and self.current_image_index > 0:
            self.current_image_index -= 1
            self.image_combo.current(self.current_image_index)
            self.load_image(self.image_files[self.current_image_index])
    
    def next_image(self):
        """下一张图片"""
        if self.image_files and self.current_image_index < len(self.image_files) - 1:
            self.current_image_index += 1
            self.image_combo.current(self.current_image_index)
            self.load_image(self.image_files[self.current_image_index])
    
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff"), ("所有文件", "*.*")]
        )
        if filename:
            self.load_image(filename)
    
    def load_image(self, image_path):
        """加载图片"""
        try:
            self.current_image_path = image_path
            
            # 使用OpenCV加载图片
            self.current_image = cv2.imread(image_path)
            if self.current_image is None:
                raise ValueError("无法读取图片")
            
            # 转换为RGB
            self.current_image = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
            
            # 计算缩放比例以适应画布
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:  # 确保画布已初始化
                img_height, img_width = self.current_image.shape[:2]
                
                scale_x = canvas_width / img_width
                scale_y = canvas_height / img_height
                self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
                
                new_width = int(img_width * self.scale_factor)
                new_height = int(img_height * self.scale_factor)
                
                # 缩放图片
                resized_image = cv2.resize(self.current_image, (new_width, new_height))
                
                # 转换为PIL图片并显示
                pil_image = Image.fromarray(resized_image)
                self.display_image = ImageTk.PhotoImage(pil_image)
                
                # 清除画布并显示图片
                self.canvas.delete("all")
                self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
                
                # 加载当前图片的标注
                self.load_current_annotations()
                
                filename = os.path.basename(image_path)
                self.status_var.set(f"已加载: {filename} (缩放: {self.scale_factor:.2f})")
            else:
                # 画布还未初始化，延迟加载
                self.root.after(100, lambda: self.load_image(image_path))
                
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")
    
    def load_current_annotations(self):
        """加载当前图片的标注"""
        filename = os.path.basename(self.current_image_path)
        self.current_boxes = self.annotations.get(filename, [])
        
        # 更新标注列表
        self.annotation_listbox.delete(0, tk.END)
        for i, box in enumerate(self.current_boxes):
            text = f"{i+1}. {box['serial']} ({box['x']},{box['y']},{box['w']},{box['h']})"
            self.annotation_listbox.insert(tk.END, text)
        
        # 在画布上绘制已有的标注框
        self.draw_existing_boxes()
    
    def draw_existing_boxes(self):
        """绘制已有的标注框"""
        for box in self.current_boxes:
            x = int(box['x'] * self.scale_factor)
            y = int(box['y'] * self.scale_factor)
            w = int(box['w'] * self.scale_factor)
            h = int(box['h'] * self.scale_factor)
            
            self.canvas.create_rectangle(x, y, x + w, y + h, outline="red", width=2, tags="annotation")
            self.canvas.create_text(x, y - 10, text=box['serial'], fill="red", anchor=tk.W, tags="annotation")
    
    def start_draw(self, event):
        """开始画框"""
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y
        
        # 删除临时矩形
        if self.current_rect:
            self.canvas.delete(self.current_rect)
    
    def draw_rect(self, event):
        """画框过程"""
        if self.drawing:
            # 删除之前的临时矩形
            if self.current_rect:
                self.canvas.delete(self.current_rect)
            
            # 画新的临时矩形
            self.current_rect = self.canvas.create_rectangle(
                self.start_x, self.start_y, event.x, event.y,
                outline="blue", width=2, tags="temp"
            )
    
    def end_draw(self, event):
        """结束画框"""
        if self.drawing:
            self.drawing = False
            
            # 计算矩形坐标
            x1, y1 = min(self.start_x, event.x), min(self.start_y, event.y)
            x2, y2 = max(self.start_x, event.x), max(self.start_y, event.y)
            
            # 检查矩形大小
            if abs(x2 - x1) > 10 and abs(y2 - y1) > 10:
                # 转换为原图坐标
                orig_x = int(x1 / self.scale_factor)
                orig_y = int(y1 / self.scale_factor)
                orig_w = int((x2 - x1) / self.scale_factor)
                orig_h = int((y2 - y1) / self.scale_factor)
                
                # 提示用户输入SERIAL数字
                self.prompt_for_serial(orig_x, orig_y, orig_w, orig_h)
            
            # 删除临时矩形
            if self.current_rect:
                self.canvas.delete(self.current_rect)
                self.current_rect = None
    
    def prompt_for_serial(self, x, y, w, h):
        """提示用户输入SERIAL数字"""
        # 清空输入框并聚焦
        self.serial_entry.delete(0, tk.END)
        self.serial_entry.focus()
        
        # 存储当前框的坐标
        self.temp_box = {'x': x, 'y': y, 'w': w, 'h': h}
        
        self.status_var.set(f"请在输入框中输入SERIAL数字，然后点击'添加标注'")
    
    def add_annotation(self):
        """添加标注"""
        serial_text = self.serial_entry.get().strip()
        if not serial_text:
            messagebox.showwarning("警告", "请输入SERIAL数字")
            return
        
        if not hasattr(self, 'temp_box'):
            messagebox.showwarning("警告", "请先在图片上画框")
            return
        
        # 添加到当前标注列表
        annotation = {
            'x': self.temp_box['x'],
            'y': self.temp_box['y'],
            'w': self.temp_box['w'],
            'h': self.temp_box['h'],
            'serial': serial_text,
            'timestamp': datetime.now().isoformat()
        }
        
        self.current_boxes.append(annotation)
        
        # 更新显示
        self.load_current_annotations()
        
        # 清空输入框
        self.serial_entry.delete(0, tk.END)
        delattr(self, 'temp_box')
        
        self.status_var.set(f"已添加标注: {serial_text}")
    
    def delete_annotation(self):
        """删除选中的标注"""
        selection = self.annotation_listbox.curselection()
        if selection:
            index = selection[0]
            del self.current_boxes[index]
            self.load_current_annotations()
            self.status_var.set("已删除标注")
    
    def clear_current_annotations(self):
        """清除当前图片的所有标注"""
        if messagebox.askyesno("确认", "确定要清除当前图片的所有标注吗？"):
            self.current_boxes = []
            self.load_current_annotations()
            self.status_var.set("已清除当前图片的所有标注")
    
    def save_annotations(self):
        """保存标注数据"""
        if self.current_image_path:
            filename = os.path.basename(self.current_image_path)
            self.annotations[filename] = self.current_boxes
        
        try:
            with open("serial_annotations.json", 'w', encoding='utf-8') as f:
                json.dump(self.annotations, f, ensure_ascii=False, indent=2)
            self.status_var.set("标注数据已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def export_training_data(self):
        """导出训练数据"""
        if not self.annotations:
            messagebox.showwarning("警告", "没有标注数据可导出")
            return
        
        try:
            # 创建训练数据文件夹
            training_folder = "training_data"
            if not os.path.exists(training_folder):
                os.makedirs(training_folder)
            
            # 导出每个标注的图片区域
            exported_count = 0
            for filename, boxes in self.annotations.items():
                if not boxes:
                    continue
                
                # 加载原图
                image_path = os.path.join("dataset", filename)
                if not os.path.exists(image_path):
                    continue
                
                img = cv2.imread(image_path)
                if img is None:
                    continue
                
                for i, box in enumerate(boxes):
                    # 提取标注区域
                    x, y, w, h = box['x'], box['y'], box['w'], box['h']
                    roi = img[y:y+h, x:x+w]
                    
                    # 保存区域图片
                    base_name = os.path.splitext(filename)[0]
                    roi_filename = f"{base_name}_serial_{i+1}_{box['serial']}.jpg"
                    roi_path = os.path.join(training_folder, roi_filename)
                    cv2.imwrite(roi_path, roi)
                    exported_count += 1
            
            # 保存标注信息
            training_info = {
                'export_time': datetime.now().isoformat(),
                'total_annotations': exported_count,
                'annotations': self.annotations
            }
            
            with open(os.path.join(training_folder, "training_info.json"), 'w', encoding='utf-8') as f:
                json.dump(training_info, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", f"已导出 {exported_count} 个训练样本到 {training_folder} 文件夹")
            self.status_var.set(f"已导出 {exported_count} 个训练样本")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SerialAnnotationTool()
    app.run()

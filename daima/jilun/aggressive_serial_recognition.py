#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激进的SERIAL识别方法
既然用户能看到SERIAL，说明信息是存在的，我们需要更激进的方法
"""

import cv2
import numpy as np
import os
import json
import pytesseract
import re
from datetime import datetime

class AggressiveSerialRecognizer:
    def __init__(self):
        self.known_serials = [
            "JAN23-7914", "JUL11-0087", "JAN23-7913", 
            "SEP23-1153", "FE823-1036", "MAR23-1055", "JUN23-1105"
        ]
    
    def load_image(self, image_path):
        """加载图片"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            print(f"❌ 加载图片失败: {str(e)}")
            return None
    
    def try_all_preprocessing_methods(self, image):
        """尝试所有可能的预处理方法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        methods = {}
        
        # 1. CLAHE + 自适应阈值 (用户确认最佳)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        methods['CLAHE_adaptive'] = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                        cv2.THRESH_BINARY, 11, 2)
        
        # 2. 不同的CLAHE参数
        clahe2 = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(16,16))
        enhanced2 = clahe2.apply(gray)
        methods['CLAHE2_adaptive'] = cv2.adaptiveThreshold(enhanced2, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                                         cv2.THRESH_BINARY, 15, 3)
        
        # 3. 强化对比度
        normalized = cv2.normalize(gray, None, 0, 255, cv2.NORM_MINMAX)
        methods['normalized_otsu'] = cv2.threshold(normalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # 4. 双边滤波 + 阈值
        bilateral = cv2.bilateralFilter(gray, 15, 80, 80)
        methods['bilateral_otsu'] = cv2.threshold(bilateral, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # 5. 高斯模糊 + 阈值
        gaussian = cv2.GaussianBlur(gray, (5, 5), 0)
        methods['gaussian_otsu'] = cv2.threshold(gaussian, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # 6. 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        morph = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        methods['morph_otsu'] = cv2.threshold(morph, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # 7. 反色处理
        methods['CLAHE_adaptive_inv'] = cv2.bitwise_not(methods['CLAHE_adaptive'])
        
        return methods
    
    def sliding_window_ocr(self, image, window_sizes=[(400, 80), (500, 100), (600, 120)]):
        """滑动窗口OCR - 在整个图片上滑动寻找SERIAL"""
        results = []
        height, width = image.shape[:2]
        
        for win_w, win_h in window_sizes:
            # 计算步长
            step_x = win_w // 4
            step_y = win_h // 2
            
            for y in range(0, height - win_h + 1, step_y):
                for x in range(0, width - win_w + 1, step_x):
                    # 提取窗口
                    window = image[y:y+win_h, x:x+win_w]
                    
                    # OCR识别
                    ocr_results = self.enhanced_ocr(window)
                    
                    for result in ocr_results:
                        if result['confidence'] > 30:  # 降低阈值
                            result['window_pos'] = (x, y, win_w, win_h)
                            results.append(result)
        
        return results
    
    def enhanced_ocr(self, roi):
        """增强OCR - 更多配置和处理"""
        if roi.size == 0:
            return []
        
        results = []
        
        # 多种放大倍数
        scales = [1, 2, 3, 4, 5, 6]
        
        for scale in scales:
            if scale > 1:
                enlarged = cv2.resize(roi, None, fx=scale, fy=scale, 
                                    interpolation=cv2.INTER_CUBIC)
            else:
                enlarged = roi
            
            # 多种OCR配置
            configs = [
                # 最宽松的配置
                '--oem 3 --psm 6',
                '--oem 3 --psm 7',
                '--oem 3 --psm 8',
                '--oem 3 --psm 13',
                # 限制字符集
                '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-',
                '--oem 3 --psm 7 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-',
                '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-',
                # 数字优先
                '--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789',
                # 字母优先
                '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            ]
            
            for config in configs:
                try:
                    text = pytesseract.image_to_string(enlarged, config=config).strip()
                    
                    if text:
                        # 多种清理方式
                        clean_versions = [
                            re.sub(r'[^A-Z0-9-]', '', text.upper()),
                            re.sub(r'[^A-Z0-9-\s]', '', text.upper()).replace(' ', ''),
                            text.upper().strip(),
                        ]
                        
                        for clean_text in clean_versions:
                            if clean_text and len(clean_text) >= 4:
                                confidence = self.calculate_confidence(clean_text)
                                if confidence > 10:  # 很低的阈值
                                    results.append({
                                        'text': clean_text,
                                        'confidence': confidence,
                                        'scale': scale,
                                        'config': config,
                                        'raw_text': text
                                    })
                except:
                    continue
        
        # 去重并排序
        unique_results = {}
        for result in results:
            text = result['text']
            if text not in unique_results or result['confidence'] > unique_results[text]['confidence']:
                unique_results[text] = result
        
        final_results = list(unique_results.values())
        final_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        return final_results
    
    def calculate_confidence(self, text):
        """计算置信度 - 更宽松的标准"""
        confidence = 0
        
        # 基础分数
        confidence += min(len(text), 15)
        
        # 包含数字
        if re.search(r'\d', text):
            confidence += 10
        
        # 包含字母
        if re.search(r'[A-Z]', text):
            confidence += 10
        
        # 包含连字符
        if '-' in text:
            confidence += 15
        
        # 长度合适
        if 6 <= len(text) <= 12:
            confidence += 20
        
        # 格式检查
        if self.is_valid_serial_format(text):
            confidence += 50
        
        # 部分匹配已知模式
        if self.partial_match_known_patterns(text):
            confidence += 25
        
        return min(confidence, 100)
    
    def partial_match_known_patterns(self, text):
        """部分匹配已知模式"""
        # 检查是否包含已知的月份
        months = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN",
                 "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"]
        
        for month in months:
            if month in text:
                return True
        
        # 检查是否包含年份模式
        if re.search(r'2[0-9]', text):  # 20, 21, 22, 23等
            return True
        
        # 检查是否包含4位数字
        if re.search(r'\d{4}', text):
            return True
        
        return False
    
    def is_valid_serial_format(self, text):
        """验证SERIAL格式"""
        patterns = [
            r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914
            r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
            r'^[A-Z]{3}\d{2}-\d{3}$',  # 3位数字变体
            r'^[A-Z]{2,4}\d{2,3}-\d{3,4}$',  # 更宽松的模式
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                return True
        return False
    
    def recognize_image_aggressively(self, image_path):
        """激进识别单张图片"""
        print(f"\n🚀 激进识别: {os.path.basename(image_path)}")
        print("-" * 50)
        
        image = self.load_image(image_path)
        if image is None:
            return []
        
        print(f"📐 图片尺寸: {image.shape[1]} x {image.shape[0]}")
        
        # 尝试所有预处理方法
        processed_images = self.try_all_preprocessing_methods(image)
        print(f"🔧 尝试 {len(processed_images)} 种预处理方法")
        
        all_results = []
        
        for method_name, processed in processed_images.items():
            print(f"\n🔍 方法: {method_name}")
            
            # 滑动窗口OCR
            window_results = self.sliding_window_ocr(processed)
            
            if window_results:
                best = max(window_results, key=lambda x: x['confidence'])
                print(f"   最佳: {best['text']} (置信度: {best['confidence']})")
                best['method'] = method_name
                all_results.append(best)
            else:
                print(f"   ❌ 无结果")
        
        # 全局排序
        all_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        return all_results
    
    def test_target_image(self):
        """测试目标图片"""
        target_image = "ceshi/微信图片_20250704123039.jpg"
        
        print("🚀 激进SERIAL识别测试")
        print("目标: 识别出 JUN23-1105")
        print("=" * 60)
        
        if not os.path.exists(target_image):
            print(f"❌ 找不到目标图片: {target_image}")
            return
        
        results = self.recognize_image_aggressively(target_image)
        
        print(f"\n📊 最终结果:")
        print("=" * 40)
        
        if results:
            print(f"找到 {len(results)} 个候选结果:")
            for i, result in enumerate(results[:10], 1):  # 显示前10个
                print(f"{i:2d}. {result['text']} (置信度: {result['confidence']}) - {result['method']}")
            
            # 检查是否找到正确答案
            correct_answer = "JUN23-1105"
            found_correct = False
            for result in results:
                if result['text'] == correct_answer:
                    found_correct = True
                    print(f"\n🎉 成功找到正确答案: {correct_answer}")
                    break
            
            if not found_correct:
                print(f"\n❌ 未找到正确答案: {correct_answer}")
                # 检查部分匹配
                for result in results:
                    if "JUN" in result['text'] or "23" in result['text'] or "1105" in result['text']:
                        print(f"🔍 部分匹配: {result['text']}")
        else:
            print("❌ 未找到任何候选结果")
        
        # 保存结果
        output_file = f"aggressive_recognition_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'target_image': target_image,
                'expected_result': 'JUN23-1105',
                'results': results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {output_file}")

def main():
    recognizer = AggressiveSerialRecognizer()
    recognizer.test_target_image()

if __name__ == "__main__":
    main()

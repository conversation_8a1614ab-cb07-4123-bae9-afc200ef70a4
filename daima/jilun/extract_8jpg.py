#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门处理8.jpg的SERIAL数字提取工具
使用最少的依赖，适合快速部署
"""

import os
import sys
from PIL import Image, ImageEnhance, ImageFilter
import re

def enhance_image_for_ocr(image_path):
    """增强图片以便更好地进行文字识别"""
    try:
        # 打开图片
        img = Image.open(image_path)
        
        # 转换为RGB模式
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # 增强对比度
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(2.0)
        
        # 增强锐度
        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(2.0)
        
        # 转换为灰度图
        img = img.convert('L')
        
        # 应用阈值处理
        threshold = 128
        img = img.point(lambda x: 255 if x > threshold else 0, mode='1')
        
        # 保存处理后的图片
        processed_path = "processed_8.jpg"
        img.save(processed_path)
        print(f"处理后的图片已保存为: {processed_path}")
        
        return processed_path
        
    except Exception as e:
        print(f"图片处理失败: {str(e)}")
        return None

def manual_ocr_guide():
    """提供手动OCR指导"""
    print("\n" + "="*60)
    print("手动识别指导")
    print("="*60)
    print("由于自动OCR可能不够准确，请按照以下步骤手动识别：")
    print()
    print("1. 查看原始图片 8.jpg")
    print("2. 查看处理后的图片 processed_8.jpg（如果生成）")
    print("3. 在图片中寻找 'SERIAL' 关键词")
    print("4. 记录SERIAL后面的数字或字母组合")
    print()
    print("常见的SERIAL格式：")
    print("- SERIAL: 123456")
    print("- SERIAL NO: ABC123")
    print("- SERIAL NUMBER: 789XYZ")
    print("- S/N: 456789")
    print()
    
    # 让用户手动输入识别结果
    while True:
        user_input = input("请输入您在图片中看到的SERIAL数字（如果没有找到请输入'无'）: ").strip()
        if user_input.lower() in ['无', 'none', 'n', '']:
            print("未找到SERIAL数字")
            break
        elif user_input:
            print(f"\n识别结果: {user_input}")
            
            # 保存结果到文件
            with open("serial_result.txt", "w", encoding="utf-8") as f:
                f.write(f"图片文件: 8.jpg\n")
                f.write(f"识别时间: {get_current_time()}\n")
                f.write(f"SERIAL数字: {user_input}\n")
            
            print(f"结果已保存到 serial_result.txt")
            break
        else:
            print("请输入有效的内容")

def get_current_time():
    """获取当前时间字符串"""
    import datetime
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def try_simple_ocr():
    """尝试简单的OCR识别"""
    try:
        import pytesseract
        from PIL import Image
        
        print("检测到pytesseract库，尝试自动OCR识别...")
        
        # 处理图片
        processed_path = enhance_image_for_ocr("8.jpg")
        if not processed_path:
            return None
        
        # 配置OCR参数
        custom_config = r'--oem 3 --psm 6'
        
        # 进行OCR识别
        img = Image.open(processed_path)
        text = pytesseract.image_to_string(img, config=custom_config)
        
        print("OCR识别的完整文本:")
        print("-" * 40)
        print(text)
        print("-" * 40)
        
        # 查找SERIAL相关信息
        lines = text.split('\n')
        serial_candidates = []
        
        for line in lines:
            line = line.strip()
            if 'SERIAL' in line.upper() or 'S/N' in line.upper():
                print(f"找到包含SERIAL的行: {line}")
                
                # 提取可能的序列号
                patterns = [
                    r'SERIAL\s*[:\-]?\s*([A-Z0-9]+)',
                    r'S/N\s*[:\-]?\s*([A-Z0-9]+)',
                    r'([A-Z0-9]{4,})',  # 4位以上的字母数字组合
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, line.upper())
                    for match in matches:
                        if match and match not in ['SERIAL', 'NUMBER']:
                            serial_candidates.append(match)
        
        if serial_candidates:
            print(f"\n可能的SERIAL数字:")
            for i, candidate in enumerate(serial_candidates, 1):
                print(f"{i}. {candidate}")
            return serial_candidates
        else:
            print("OCR未能识别出SERIAL数字")
            return None
            
    except ImportError:
        print("未安装pytesseract库，将使用手动识别模式")
        return None
    except Exception as e:
        print(f"OCR识别失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("="*60)
    print("8.jpg SERIAL数字提取工具")
    print("="*60)
    
    # 检查8.jpg是否存在
    if not os.path.exists("8.jpg"):
        print("错误：当前目录下没有找到 8.jpg 文件")
        print("请将8.jpg文件放在与此脚本相同的目录下")
        input("按Enter键退出...")
        return
    
    print("找到8.jpg文件，开始处理...")
    
    # 尝试自动OCR
    serial_numbers = try_simple_ocr()
    
    if serial_numbers:
        print(f"\n自动识别完成！找到 {len(serial_numbers)} 个可能的SERIAL数字")
        
        # 保存结果
        with open("serial_result.txt", "w", encoding="utf-8") as f:
            f.write(f"图片文件: 8.jpg\n")
            f.write(f"识别时间: {get_current_time()}\n")
            f.write(f"识别方法: 自动OCR\n")
            f.write(f"找到的SERIAL数字:\n")
            for i, serial in enumerate(serial_numbers, 1):
                f.write(f"{i}. {serial}\n")
        
        print("结果已保存到 serial_result.txt")
    else:
        print("\n自动识别失败，切换到手动识别模式")
        # 增强图片
        enhance_image_for_ocr("8.jpg")
        # 手动识别指导
        manual_ocr_guide()
    
    print("\n处理完成！")
    input("按Enter键退出...")

if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
title 在原图片上显示标注内容

echo ========================================
echo 在原图片上显示标注内容
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python
    echo.
    echo 请先安装Python：
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本的Python
    echo 3. 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Python已安装，版本信息：
python --version
echo.

REM 检查标注数据文件
if not exist "serial_annotations.json" (
    echo 警告：未找到标注数据文件 serial_annotations.json
    echo 请先使用标注工具创建标注数据
    echo.
    pause
    exit /b 1
)

REM 检查并安装必要的库
echo 正在检查必要的库...

python -c "import cv2" >nul 2>&1
if errorlevel 1 (
    echo 正在安装opencv-python库...
    pip install opencv-python
    if errorlevel 1 (
        echo 错误：opencv-python库安装失败
        pause
        exit /b 1
    ) else (
        echo opencv-python库安装成功
    )
) else (
    echo opencv-python库已安装
)

python -c "import numpy" >nul 2>&1
if errorlevel 1 (
    echo 正在安装numpy库...
    pip install numpy
    if errorlevel 1 (
        echo 错误：numpy库安装失败
        pause
        exit /b 1
    ) else (
        echo numpy库安装成功
    )
) else (
    echo numpy库已安装
)

echo.
echo 所有依赖库检查完成！
echo.
echo 功能说明：
echo 1. 在原图片上绘制红色标注框
echo 2. 显示SERIAL数字内容
echo 3. 添加标注序号
echo 4. 保存到annotated_images文件夹
echo.
echo 正在启动标注可视化工具...
echo.

REM 运行标注可视化工具
if exist "show_annotations_on_image.py" (
    python show_annotations_on_image.py
) else (
    echo 错误：找不到 show_annotations_on_image.py 文件
    echo 请确保所有文件都在同一文件夹中
    pause
    exit /b 1
)

echo.
echo 标注可视化工具已关闭
pause

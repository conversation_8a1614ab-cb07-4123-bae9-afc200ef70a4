  #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SERIAL数字标注工具
专门用于标注dataset文件夹中SERIAL后面的数字
帮助训练数字识别模型
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk
import os
import json
from datetime import datetime

class SerialNumberAnnotationTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SERIAL数字标注工具 - 专业版")
        self.root.geometry("1400x900")

        # 数据存储
        self.dataset_folder = "dataset"
        self.annotations = {}  # 存储所有标注数据
        self.current_image_path = None
        self.current_image = None
        self.original_image = None
        self.current_boxes = []  # 当前图片的标注框
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.current_rect = None

        # 图片显示相关
        self.canvas_width = 800
        self.canvas_height = 600
        self.scale_factor = 1.0

        # 旋转相关
        self.rotation_angle = 0

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请选择图片开始标注")

        # 创建界面
        self.create_widgets()
        self.load_annotations()
        self.load_image_list()

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        left_frame = ttk.Frame(main_frame, width=350)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)

        # 右侧图片显示区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        self.create_left_panel(left_frame)
        self.create_right_panel(right_frame)

        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=10, pady=5)

    def create_left_panel(self, parent):
        """创建左侧控制面板"""
        # 标题
        title_label = ttk.Label(parent, text="SERIAL数字标注工具", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 图片选择区域
        img_frame = ttk.LabelFrame(parent, text="图片选择", padding=10)
        img_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(img_frame, text="选择图片:").pack(anchor=tk.W)
        self.image_var = tk.StringVar()
        self.image_combo = ttk.Combobox(img_frame, textvariable=self.image_var, state="readonly", width=40)
        self.image_combo.pack(fill=tk.X, pady=(5, 10))
        self.image_combo.bind('<<ComboboxSelected>>', self.on_image_selected)

        # 图片导航按钮
        nav_frame = ttk.Frame(img_frame)
        nav_frame.pack(fill=tk.X, pady=(5, 10))

        ttk.Button(nav_frame, text="⬅️ 上一张", command=self.previous_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(nav_frame, text="➡️ 下一张", command=self.next_image).pack(side=tk.LEFT, padx=(0, 5))

        # 显示图片进度
        self.progress_var = tk.StringVar()
        self.progress_var.set("图片: 0/0")
        ttk.Label(nav_frame, textvariable=self.progress_var, font=("Arial", 9)).pack(side=tk.RIGHT)

        # 显示当前图片路径
        self.path_var = tk.StringVar()
        path_label = ttk.Label(img_frame, textvariable=self.path_var, foreground="gray", font=("Arial", 8))
        path_label.pack(anchor=tk.W)

        # 图片旋转区域
        rotation_frame = ttk.LabelFrame(parent, text="图片旋转", padding=10)
        rotation_frame.pack(fill=tk.X, pady=(0, 10))

        rotation_buttons = ttk.Frame(rotation_frame)
        rotation_buttons.pack(fill=tk.X)

        ttk.Button(rotation_buttons, text="↺ 逆时针90°", command=self.rotate_counterclockwise).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(rotation_buttons, text="↻ 顺时针90°", command=self.rotate_clockwise).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(rotation_buttons, text="🔄 重置", command=self.reset_rotation).pack(side=tk.LEFT)

        self.angle_var = tk.StringVar()
        self.angle_var.set("当前角度: 0°")
        ttk.Label(rotation_frame, textvariable=self.angle_var).pack(pady=(10, 0))

        # 标注信息区域
        annotation_frame = ttk.LabelFrame(parent, text="标注信息", padding=10)
        annotation_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(annotation_frame, text="SERIAL数字:").pack(anchor=tk.W)
        self.serial_var = tk.StringVar()
        self.serial_entry = ttk.Entry(annotation_frame, textvariable=self.serial_var, font=("Arial", 12))
        self.serial_entry.pack(fill=tk.X, pady=(5, 10))

        # 添加标注按钮
        ttk.Button(annotation_frame, text="添加标注", command=self.add_annotation).pack(fill=tk.X, pady=(0, 5))

        # 操作按钮区域
        button_frame = ttk.LabelFrame(parent, text="操作", padding=10)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(button_frame, text="保存标注", command=self.save_annotations).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="导出训练数据", command=self.export_training_data).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="保存标注图片", command=self.save_annotated_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="清除当前标注", command=self.clear_current_annotations).pack(fill=tk.X)

        # 当前图片标注列表
        list_frame = ttk.LabelFrame(parent, text="当前图片标注", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)

        self.annotation_listbox = tk.Listbox(list_container, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.annotation_listbox.yview)
        self.annotation_listbox.configure(yscrollcommand=scrollbar.set)

        self.annotation_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 删除选中标注按钮
        ttk.Button(list_frame, text="删除选中标注", command=self.delete_selected_annotation).pack(fill=tk.X, pady=(10, 0))

    def create_right_panel(self, parent):
        """创建右侧图片显示面板"""
        # 图片显示区域
        canvas_frame = ttk.LabelFrame(parent, text="图片显示区域", padding=10)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # 创建画布
        self.canvas = tk.Canvas(canvas_frame, width=self.canvas_width, height=self.canvas_height,
                               bg="white", cursor="crosshair")
        self.canvas.pack(expand=True)

        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.start_draw)
        self.canvas.bind("<B1-Motion>", self.draw_rect)
        self.canvas.bind("<ButtonRelease-1>", self.end_draw)

        # 使用说明
        help_frame = ttk.LabelFrame(parent, text="使用说明", padding=10)
        help_frame.pack(fill=tk.X, pady=(10, 0))

        help_text = """
1. 选择要标注的图片
2. 如需要，旋转图片到合适角度
3. 在图片上拖拽鼠标画框选中SERIAL数字
4. 在左侧输入框中输入识别出的数字
5. 点击"添加标注"保存当前标注
6. 重复步骤3-5标注所有SERIAL数字
7. 点击"保存标注"保存到文件
        """
        ttk.Label(help_frame, text=help_text, justify=tk.LEFT, font=("Arial", 9)).pack(anchor=tk.W)

    def load_image_list(self):
        """加载图片列表"""
        if not os.path.exists(self.dataset_folder):
            messagebox.showerror("错误", f"找不到dataset文件夹: {self.dataset_folder}")
            return

        image_files = []
        for file in os.listdir(self.dataset_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)

        image_files.sort()
        self.image_combo['values'] = image_files

        if image_files:
            self.image_combo.current(0)
            self.on_image_selected(None)
            self.status_var.set(f"已加载 {len(image_files)} 张图片")
        else:
            messagebox.showwarning("警告", "dataset文件夹中没有找到图片文件")

    def on_image_selected(self, event):
        """图片选择事件"""
        selected_image = self.image_var.get()
        if not selected_image:
            return

        self.current_image_path = os.path.join(self.dataset_folder, selected_image)
        self.path_var.set(f"路径: {self.current_image_path}")

        # 重置旋转角度
        self.rotation_angle = 0
        self.angle_var.set("当前角度: 0°")

        # 加载图片
        self.load_current_image()

        # 加载当前图片的标注
        self.load_current_annotations()

        self.status_var.set(f"已加载图片: {selected_image}")

    def load_current_image(self):
        """加载当前选中的图片"""
        if not self.current_image_path or not os.path.exists(self.current_image_path):
            return

        try:
            # 使用OpenCV加载图片
            self.original_image = cv2.imread(self.current_image_path)
            if self.original_image is None:
                messagebox.showerror("错误", "无法加载图片")
                return

            # 应用旋转
            self.current_image = self.apply_rotation(self.original_image)

            # 转换为RGB格式
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)

            # 计算缩放比例
            img_height, img_width = image_rgb.shape[:2]
            scale_x = self.canvas_width / img_width
            scale_y = self.canvas_height / img_height
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大，只缩小

            # 缩放图片
            new_width = int(img_width * self.scale_factor)
            new_height = int(img_height * self.scale_factor)

            image_resized = cv2.resize(image_rgb, (new_width, new_height))

            # 转换为PIL格式并显示
            pil_image = Image.fromarray(image_resized)
            self.photo = ImageTk.PhotoImage(pil_image)

            # 清除画布并显示图片
            self.canvas.delete("all")
            self.canvas.create_image(self.canvas_width//2, self.canvas_height//2,
                                   image=self.photo, anchor=tk.CENTER)

            # 显示现有标注
            self.display_annotations()

        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")

    def apply_rotation(self, image):
        """应用旋转变换"""
        if self.rotation_angle == 0:
            return image.copy()

        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # 创建旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, self.rotation_angle, 1.0)

        # 计算旋转后的图片尺寸
        cos_val = abs(rotation_matrix[0, 0])
        sin_val = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_val) + (width * cos_val))
        new_height = int((height * cos_val) + (width * sin_val))

        # 调整旋转矩阵的平移部分
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]

        # 应用旋转
        rotated_image = cv2.warpAffine(image, rotation_matrix, (new_width, new_height),
                                     flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT,
                                     borderValue=(255, 255, 255))

        return rotated_image

    def rotate_counterclockwise(self):
        """逆时针旋转90度"""
        self.rotation_angle = (self.rotation_angle + 90) % 360
        self.angle_var.set(f"当前角度: {self.rotation_angle}°")
        self.load_current_image()

    def rotate_clockwise(self):
        """顺时针旋转90度"""
        self.rotation_angle = (self.rotation_angle - 90) % 360
        self.angle_var.set(f"当前角度: {self.rotation_angle}°")
        self.load_current_image()

    def reset_rotation(self):
        """重置旋转角度"""
        self.rotation_angle = 0
        self.angle_var.set("当前角度: 0°")
        self.load_current_image()

    def convert_to_original_coordinates(self, x, y, w, h):
        """将显示坐标转换为原图坐标"""
        # 先转换为当前旋转图片的实际坐标
        actual_x = int(x / self.scale_factor)
        actual_y = int(y / self.scale_factor)
        actual_w = int(w / self.scale_factor)
        actual_h = int(h / self.scale_factor)

        if self.rotation_angle == 0:
            return actual_x, actual_y, actual_w, actual_h

        # 如果有旋转，需要转换回原图坐标
        # 这里简化处理，实际应用中可能需要更复杂的坐标变换
        return actual_x, actual_y, actual_w, actual_h

    def convert_from_original_coordinates(self, x, y, w, h):
        """将原图坐标转换为显示坐标"""
        # 如果有旋转，先应用旋转变换
        # 这里简化处理
        actual_x, actual_y, actual_w, actual_h = x, y, w, h

        # 转换为显示坐标
        disp_x = actual_x * self.scale_factor
        disp_y = actual_y * self.scale_factor
        disp_w = actual_w * self.scale_factor
        disp_h = actual_h * self.scale_factor

        return disp_x, disp_y, disp_w, disp_h

    def load_annotations(self):
        """加载标注数据"""
        annotation_file = "serial_number_annotations.json"
        if os.path.exists(annotation_file):
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    self.annotations = json.load(f)
                print(f"已加载标注数据，包含 {len(self.annotations)} 个文件的标注")
            except Exception as e:
                print(f"加载标注数据失败: {str(e)}")
                self.annotations = {}
        else:
            self.annotations = {}

    def save_annotations(self):
        """保存标注数据"""
        try:
            annotation_file = "serial_number_annotations.json"
            with open(annotation_file, 'w', encoding='utf-8') as f:
                json.dump(self.annotations, f, ensure_ascii=False, indent=2)

            total_annotations = sum(len(boxes) for boxes in self.annotations.values())
            messagebox.showinfo("成功", f"标注数据已保存\n文件: {annotation_file}\n总标注数: {total_annotations}")
            self.status_var.set(f"已保存标注数据，总计 {total_annotations} 个标注")

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {str(e)}")

    def load_current_annotations(self):
        """加载当前图片的标注"""
        if not self.current_image_path:
            return

        filename = os.path.basename(self.current_image_path)
        self.current_boxes = self.annotations.get(filename, [])

        # 更新标注列表
        self.update_annotation_list()

        # 显示标注框
        self.display_annotations()

    def update_annotation_list(self):
        """更新标注列表显示"""
        self.annotation_listbox.delete(0, tk.END)

        for i, box in enumerate(self.current_boxes):
            serial = box['serial']
            x, y, w, h = box['x'], box['y'], box['w'], box['h']
            text = f"{i+1}. {serial} - 位置:({x},{y}) 大小:{w}x{h}"
            self.annotation_listbox.insert(tk.END, text)

    def display_annotations(self):
        """在画布上显示标注框"""
        # 清除之前的标注显示
        self.canvas.delete("annotation")

        if not self.current_boxes:
            return

        # 获取画布中心和图片位置
        canvas_center_x = self.canvas_width // 2
        canvas_center_y = self.canvas_height // 2

        if hasattr(self, 'current_image'):
            img_height, img_width = self.current_image.shape[:2]
            display_width = int(img_width * self.scale_factor)
            display_height = int(img_height * self.scale_factor)

            # 计算图片在画布中的偏移
            offset_x = canvas_center_x - display_width // 2
            offset_y = canvas_center_y - display_height // 2

            for i, box in enumerate(self.current_boxes):
                # 转换坐标
                disp_x, disp_y, disp_w, disp_h = self.convert_from_original_coordinates(
                    box['x'], box['y'], box['w'], box['h'])

                # 调整到画布坐标
                x = offset_x + disp_x
                y = offset_y + disp_y

                # 绘制标注框
                self.canvas.create_rectangle(x, y, x + disp_w, y + disp_h,
                                           outline="red", width=2, tags="annotation")

                # 绘制SERIAL文字背景
                text = box['serial']
                text_x = x
                text_y = y - 15 if y > 20 else y + disp_h + 15

                # 创建文字背景
                bbox = self.canvas.create_text(text_x, text_y, text=text, fill="red",
                                             anchor=tk.W, tags="annotation", font=("Arial", 10, "bold"))
                text_bbox = self.canvas.bbox(bbox)
                if text_bbox:
                    self.canvas.create_rectangle(text_bbox[0]-2, text_bbox[1]-2,
                                               text_bbox[2]+2, text_bbox[3]+2,
                                               fill="white", outline="red", width=1, tags="annotation")
                    # 重新绘制文字在背景之上
                    self.canvas.delete(bbox)
                    self.canvas.create_text(text_x, text_y, text=text, fill="red",
                                          anchor=tk.W, tags="annotation", font=("Arial", 10, "bold"))

                # 绘制序号
                number_text = f"{i+1}"
                self.canvas.create_text(x + 5, y + 15, text=number_text, fill="blue",
                                      anchor=tk.W, tags="annotation", font=("Arial", 8, "bold"))

    def start_draw(self, event):
        """开始画框"""
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y

        # 删除之前的临时矩形
        if self.current_rect:
            self.canvas.delete(self.current_rect)

    def draw_rect(self, event):
        """绘制矩形框"""
        if not self.drawing:
            return

        # 删除之前的临时矩形
        if self.current_rect:
            self.canvas.delete(self.current_rect)

        # 绘制新的临时矩形
        self.current_rect = self.canvas.create_rectangle(
            self.start_x, self.start_y, event.x, event.y,
            outline="blue", width=2, tags="temp"
        )

    def end_draw(self, event):
        """结束画框"""
        if not self.drawing:
            return

        self.drawing = False

        # 删除临时矩形
        if self.current_rect:
            self.canvas.delete(self.current_rect)
            self.current_rect = None

        # 计算矩形坐标
        x1, y1 = self.start_x, self.start_y
        x2, y2 = event.x, event.y

        # 确保坐标正确
        x = min(x1, x2)
        y = min(y1, y2)
        w = abs(x2 - x1)
        h = abs(y2 - y1)

        # 检查矩形大小
        if w < 10 or h < 10:
            self.status_var.set("标注框太小，请重新绘制")
            return

        # 转换为相对于图片的坐标
        if hasattr(self, 'current_image'):
            canvas_center_x = self.canvas_width // 2
            canvas_center_y = self.canvas_height // 2
            img_height, img_width = self.current_image.shape[:2]
            display_width = int(img_width * self.scale_factor)
            display_height = int(img_height * self.scale_factor)

            offset_x = canvas_center_x - display_width // 2
            offset_y = canvas_center_y - display_height // 2

            # 调整坐标到图片坐标系
            img_x = x - offset_x
            img_y = y - offset_y

            # 检查是否在图片范围内
            if img_x < 0 or img_y < 0 or img_x + w > display_width or img_y + h > display_height:
                self.status_var.set("标注框超出图片范围，请重新绘制")
                return

            # 转换为原图坐标
            orig_x, orig_y, orig_w, orig_h = self.convert_to_original_coordinates(img_x, img_y, w, h)

            # 提示用户输入SERIAL数字
            self.status_var.set(f"已选择区域 ({orig_x}, {orig_y}, {orig_w}, {orig_h})，请输入SERIAL数字")
            self.serial_entry.focus()

            # 临时存储当前选择的区域
            self.temp_box = {
                'x': orig_x,
                'y': orig_y,
                'w': orig_w,
                'h': orig_h
            }

    def add_annotation(self):
        """添加标注"""
        if not hasattr(self, 'temp_box'):
            messagebox.showwarning("警告", "请先在图片上画框选择区域")
            return

        serial_text = self.serial_var.get().strip()
        if not serial_text:
            messagebox.showwarning("警告", "请输入SERIAL数字")
            self.serial_entry.focus()
            return

        # 创建标注数据
        annotation = {
            'x': self.temp_box['x'],
            'y': self.temp_box['y'],
            'w': self.temp_box['w'],
            'h': self.temp_box['h'],
            'serial': serial_text,
            'timestamp': datetime.now().isoformat()
        }

        # 添加到当前图片的标注列表
        self.current_boxes.append(annotation)

        # 保存到总标注数据
        filename = os.path.basename(self.current_image_path)
        self.annotations[filename] = self.current_boxes

        # 更新显示
        self.update_annotation_list()
        self.display_annotations()

        # 清空输入框
        self.serial_var.set("")

        # 删除临时区域
        if hasattr(self, 'temp_box'):
            delattr(self, 'temp_box')

        self.status_var.set(f"已添加标注: {serial_text}")

        # 自动保存
        self.save_annotations()

    def delete_selected_annotation(self):
        """删除选中的标注"""
        selection = self.annotation_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的标注")
            return

        index = selection[0]
        if 0 <= index < len(self.current_boxes):
            deleted_annotation = self.current_boxes.pop(index)

            # 更新总标注数据
            filename = os.path.basename(self.current_image_path)
            self.annotations[filename] = self.current_boxes

            # 更新显示
            self.update_annotation_list()
            self.display_annotations()

            self.status_var.set(f"已删除标注: {deleted_annotation['serial']}")

            # 自动保存
            self.save_annotations()

    def clear_current_annotations(self):
        """清除当前图片的所有标注"""
        if not self.current_boxes:
            messagebox.showinfo("信息", "当前图片没有标注")
            return

        result = messagebox.askyesno("确认", "确定要清除当前图片的所有标注吗？")
        if result:
            self.current_boxes = []
            filename = os.path.basename(self.current_image_path)
            if filename in self.annotations:
                del self.annotations[filename]

            self.update_annotation_list()
            self.display_annotations()
            self.status_var.set("已清除当前图片的所有标注")

            # 自动保存
            self.save_annotations()

    def export_training_data(self):
        """导出训练数据"""
        if not self.annotations:
            messagebox.showwarning("警告", "没有标注数据可导出")
            return

        try:
            # 创建训练数据文件夹
            training_folder = "serial_training_data"
            if not os.path.exists(training_folder):
                os.makedirs(training_folder)

            exported_count = 0
            training_info = []

            for filename, boxes in self.annotations.items():
                if not boxes:
                    continue

                # 加载原图
                image_path = os.path.join(self.dataset_folder, filename)
                if not os.path.exists(image_path):
                    continue

                img = cv2.imread(image_path)
                if img is None:
                    continue

                for i, box in enumerate(boxes):
                    # 提取标注区域（使用原图坐标）
                    x, y, w, h = box['x'], box['y'], box['w'], box['h']

                    # 确保坐标在图片范围内
                    img_height, img_width = img.shape[:2]
                    x = max(0, min(x, img_width - 1))
                    y = max(0, min(y, img_height - 1))
                    w = min(w, img_width - x)
                    h = min(h, img_height - y)

                    if w > 0 and h > 0:
                        roi = img[y:y+h, x:x+w]

                        # 保存区域图片
                        base_name = os.path.splitext(filename)[0]
                        roi_filename = f"{base_name}_serial_{i+1}_{box['serial']}.jpg"
                        roi_path = os.path.join(training_folder, roi_filename)
                        cv2.imwrite(roi_path, roi)
                        exported_count += 1

                        # 记录训练信息
                        training_info.append({
                            'filename': roi_filename,
                            'original_image': filename,
                            'serial_number': box['serial'],
                            'bbox': [x, y, w, h],
                            'timestamp': box.get('timestamp', '')
                        })

            # 保存训练信息
            info_file = os.path.join(training_folder, "training_info.json")
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(training_info, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", f"训练数据导出完成！\n导出数量: {exported_count}\n保存位置: {training_folder}")
            self.status_var.set(f"已导出 {exported_count} 个训练样本到 {training_folder}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def save_annotated_image(self):
        """保存带标注的图片"""
        if not self.current_image_path or not self.current_boxes:
            messagebox.showwarning("警告", "没有图片或标注数据可保存")
            return

        try:
            # 加载原图
            original_img = cv2.imread(self.current_image_path)
            if original_img is None:
                messagebox.showerror("错误", "无法加载原图片")
                return

            # 复制图片以避免修改原图
            annotated_img = original_img.copy()

            # 绘制每个标注框
            for i, box in enumerate(self.current_boxes):
                x, y, w, h = box['x'], box['y'], box['w'], box['h']
                serial = box['serial']

                # 确保坐标在图片范围内
                img_height, img_width = original_img.shape[:2]
                x = max(0, min(x, img_width - 1))
                y = max(0, min(y, img_height - 1))
                w = min(w, img_width - x)
                h = min(h, img_height - y)

                if w <= 0 or h <= 0:
                    continue

                # 绘制矩形框
                color = (0, 0, 255)  # 红色 (BGR格式)
                thickness = 3
                cv2.rectangle(annotated_img, (x, y), (x + w, y + h), color, thickness)

                # 计算文字大小和位置
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 1.0
                text_thickness = 2

                # 获取文字尺寸
                (text_width, text_height), baseline = cv2.getTextSize(serial, font, font_scale, text_thickness)

                # 计算文字背景框位置
                text_x = x
                text_y = y - 10 if y - 10 > text_height else y + h + text_height + 10

                # 确保文字在图片范围内
                if text_x + text_width > img_width:
                    text_x = img_width - text_width
                if text_y < text_height:
                    text_y = text_height
                if text_y > img_height:
                    text_y = img_height - 5

                # 绘制文字背景
                bg_x1 = text_x - 5
                bg_y1 = text_y - text_height - 5
                bg_x2 = text_x + text_width + 5
                bg_y2 = text_y + baseline + 5

                cv2.rectangle(annotated_img, (bg_x1, bg_y1), (bg_x2, bg_y2), (255, 255, 255), -1)
                cv2.rectangle(annotated_img, (bg_x1, bg_y1), (bg_x2, bg_y2), color, 2)

                # 绘制文字
                cv2.putText(annotated_img, serial, (text_x, text_y), font, font_scale, color, text_thickness)

                # 绘制标注序号
                number_text = f"{i+1}"
                number_x = x + 5
                number_y = y + 25
                cv2.putText(annotated_img, number_text, (number_x, number_y), font, 0.7, (255, 255, 0), 2)

            # 创建标注图片文件夹
            annotated_folder = "annotated_images"
            if not os.path.exists(annotated_folder):
                os.makedirs(annotated_folder)

            # 生成输出文件名
            filename = os.path.basename(self.current_image_path)
            base_name = os.path.splitext(filename)[0]
            output_filename = f"annotated_{base_name}.jpg"
            output_path = os.path.join(annotated_folder, output_filename)

            # 保存图片
            success = cv2.imwrite(output_path, annotated_img)
            if success:
                messagebox.showinfo("成功", f"已保存标注图片到文件夹:\n{output_path}")
                self.status_var.set(f"已保存标注图片: {output_path}")
            else:
                messagebox.showerror("错误", "保存图片失败")

        except Exception as e:
            messagebox.showerror("错误", f"保存标注图片失败: {str(e)}")

    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    app = SerialNumberAnnotationTool()
    app.run()

if __name__ == "__main__":
    main()
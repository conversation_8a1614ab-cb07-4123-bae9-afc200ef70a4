#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SERIAL数字标注工具
专门用于标注dataset文件夹中SERIAL后面的数字
帮助训练数字识别模型
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk
import os
import json
from datetime import datetime

class SerialNumberAnnotationTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SERIAL数字标注工具 - 专业版")
        self.root.geometry("1400x900")

        # 数据存储
        self.dataset_folder = "dataset"
        self.annotations = {}  # 存储所有标注数据
        self.current_image_path = None
        self.current_image = None
        self.original_image = None
        self.current_boxes = []  # 当前图片的标注框
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.current_rect = None

        # 图片显示相关
        self.canvas_width = 800
        self.canvas_height = 600
        self.scale_factor = 1.0

        # 旋转相关
        self.rotation_angle = 0

        # 创建界面
        self.create_widgets()
        self.load_annotations()
        self.load_image_list()

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请选择图片开始标注")

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        left_frame = ttk.Frame(main_frame, width=350)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)

        # 右侧图片显示区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        self.create_left_panel(left_frame)
        self.create_right_panel(right_frame)

        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=10, pady=5)

    def create_left_panel(self, parent):
        """创建左侧控制面板"""
        # 标题
        title_label = ttk.Label(parent, text="SERIAL数字标注工具", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 图片选择区域
        img_frame = ttk.LabelFrame(parent, text="图片选择", padding=10)
        img_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(img_frame, text="选择图片:").pack(anchor=tk.W)
        self.image_var = tk.StringVar()
        self.image_combo = ttk.Combobox(img_frame, textvariable=self.image_var, state="readonly", width=40)
        self.image_combo.pack(fill=tk.X, pady=(5, 10))
        self.image_combo.bind('<<ComboboxSelected>>', self.on_image_selected)

        # 显示当前图片路径
        self.path_var = tk.StringVar()
        path_label = ttk.Label(img_frame, textvariable=self.path_var, foreground="gray", font=("Arial", 8))
        path_label.pack(anchor=tk.W)

        # 图片旋转区域
        rotation_frame = ttk.LabelFrame(parent, text="图片旋转", padding=10)
        rotation_frame.pack(fill=tk.X, pady=(0, 10))

        rotation_buttons = ttk.Frame(rotation_frame)
        rotation_buttons.pack(fill=tk.X)

        ttk.Button(rotation_buttons, text="↺ 逆时针90°", command=self.rotate_counterclockwise).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(rotation_buttons, text="↻ 顺时针90°", command=self.rotate_clockwise).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(rotation_buttons, text="🔄 重置", command=self.reset_rotation).pack(side=tk.LEFT)

        self.angle_var = tk.StringVar()
        self.angle_var.set("当前角度: 0°")
        ttk.Label(rotation_frame, textvariable=self.angle_var).pack(pady=(10, 0))

        # 标注信息区域
        annotation_frame = ttk.LabelFrame(parent, text="标注信息", padding=10)
        annotation_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(annotation_frame, text="SERIAL数字:").pack(anchor=tk.W)
        self.serial_var = tk.StringVar()
        self.serial_entry = ttk.Entry(annotation_frame, textvariable=self.serial_var, font=("Arial", 12))
        self.serial_entry.pack(fill=tk.X, pady=(5, 10))

        # 添加标注按钮
        ttk.Button(annotation_frame, text="添加标注", command=self.add_annotation).pack(fill=tk.X, pady=(0, 5))

        # 操作按钮区域
        button_frame = ttk.LabelFrame(parent, text="操作", padding=10)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(button_frame, text="保存标注", command=self.save_annotations).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="导出训练数据", command=self.export_training_data).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="保存标注图片", command=self.save_annotated_image).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(button_frame, text="清除当前标注", command=self.clear_current_annotations).pack(fill=tk.X)

        # 当前图片标注列表
        list_frame = ttk.LabelFrame(parent, text="当前图片标注", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)

        self.annotation_listbox = tk.Listbox(list_container, font=("Arial", 10))
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.annotation_listbox.yview)
        self.annotation_listbox.configure(yscrollcommand=scrollbar.set)

        self.annotation_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 删除选中标注按钮
        ttk.Button(list_frame, text="删除选中标注", command=self.delete_selected_annotation).pack(fill=tk.X, pady=(10, 0))

    def create_right_panel(self, parent):
        """创建右侧图片显示面板"""
        # 图片显示区域
        canvas_frame = ttk.LabelFrame(parent, text="图片显示区域", padding=10)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # 创建画布
        self.canvas = tk.Canvas(canvas_frame, width=self.canvas_width, height=self.canvas_height,
                               bg="white", cursor="crosshair")
        self.canvas.pack(expand=True)

        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.start_draw)
        self.canvas.bind("<B1-Motion>", self.draw_rect)
        self.canvas.bind("<ButtonRelease-1>", self.end_draw)

        # 使用说明
        help_frame = ttk.LabelFrame(parent, text="使用说明", padding=10)
        help_frame.pack(fill=tk.X, pady=(10, 0))

        help_text = """
1. 选择要标注的图片
2. 如需要，旋转图片到合适角度
3. 在图片上拖拽鼠标画框选中SERIAL数字
4. 在左侧输入框中输入识别出的数字
5. 点击"添加标注"保存当前标注
6. 重复步骤3-5标注所有SERIAL数字
7. 点击"保存标注"保存到文件
        """
        ttk.Label(help_frame, text=help_text, justify=tk.LEFT, font=("Arial", 9)).pack(anchor=tk.W)

    def load_image_list(self):
        """加载图片列表"""
        if not os.path.exists(self.dataset_folder):
            messagebox.showerror("错误", f"找不到dataset文件夹: {self.dataset_folder}")
            return

        image_files = []
        for file in os.listdir(self.dataset_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)

        image_files.sort()
        self.image_combo['values'] = image_files

        if image_files:
            self.image_combo.current(0)
            self.on_image_selected(None)
            self.status_var.set(f"已加载 {len(image_files)} 张图片")
        else:
            messagebox.showwarning("警告", "dataset文件夹中没有找到图片文件")

    def on_image_selected(self, event):
        """图片选择事件"""
        selected_image = self.image_var.get()
        if not selected_image:
            return

        self.current_image_path = os.path.join(self.dataset_folder, selected_image)
        self.path_var.set(f"路径: {self.current_image_path}")

        # 重置旋转角度
        self.rotation_angle = 0
        self.angle_var.set("当前角度: 0°")

        # 加载图片
        self.load_current_image()

        # 加载当前图片的标注
        self.load_current_annotations()

        self.status_var.set(f"已加载图片: {selected_image}")

    def load_current_image(self):
        """加载当前选中的图片"""
        if not self.current_image_path or not os.path.exists(self.current_image_path):
            return

        try:
            # 使用OpenCV加载图片
            self.original_image = cv2.imread(self.current_image_path)
            if self.original_image is None:
                messagebox.showerror("错误", "无法加载图片")
                return

            # 应用旋转
            self.current_image = self.apply_rotation(self.original_image)

            # 转换为RGB格式
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)

            # 计算缩放比例
            img_height, img_width = image_rgb.shape[:2]
            scale_x = self.canvas_width / img_width
            scale_y = self.canvas_height / img_height
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大，只缩小

            # 缩放图片
            new_width = int(img_width * self.scale_factor)
            new_height = int(img_height * self.scale_factor)

            image_resized = cv2.resize(image_rgb, (new_width, new_height))

            # 转换为PIL格式并显示
            pil_image = Image.fromarray(image_resized)
            self.photo = ImageTk.PhotoImage(pil_image)

            # 清除画布并显示图片
            self.canvas.delete("all")
            self.canvas.create_image(self.canvas_width//2, self.canvas_height//2,
                                   image=self.photo, anchor=tk.CENTER)

            # 显示现有标注
            self.display_annotations()

        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")

    def apply_rotation(self, image):
        """应用旋转变换"""
        if self.rotation_angle == 0:
            return image.copy()

        height, width = image.shape[:2]
        center = (width // 2, height // 2)

        # 创建旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, self.rotation_angle, 1.0)

        # 计算旋转后的图片尺寸
        cos_val = abs(rotation_matrix[0, 0])
        sin_val = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_val) + (width * cos_val))
        new_height = int((height * cos_val) + (width * sin_val))

        # 调整旋转矩阵的平移部分
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]

        # 应用旋转
        rotated_image = cv2.warpAffine(image, rotation_matrix, (new_width, new_height),
                                     flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT,
                                     borderValue=(255, 255, 255))

        return rotated_image

    def rotate_counterclockwise(self):
        """逆时针旋转90度"""
        self.rotation_angle = (self.rotation_angle + 90) % 360
        self.angle_var.set(f"当前角度: {self.rotation_angle}°")
        self.load_current_image()

    def rotate_clockwise(self):
        """顺时针旋转90度"""
        self.rotation_angle = (self.rotation_angle - 90) % 360
        self.angle_var.set(f"当前角度: {self.rotation_angle}°")
        self.load_current_image()

    def reset_rotation(self):
        """重置旋转角度"""
        self.rotation_angle = 0
        self.angle_var.set("当前角度: 0°")
        self.load_current_image()

    def convert_to_original_coordinates(self, x, y, w, h):
        """将显示坐标转换为原图坐标"""
        # 先转换为当前旋转图片的实际坐标
        actual_x = int(x / self.scale_factor)
        actual_y = int(y / self.scale_factor)
        actual_w = int(w / self.scale_factor)
        actual_h = int(h / self.scale_factor)

        if self.rotation_angle == 0:
            return actual_x, actual_y, actual_w, actual_h

        # 如果有旋转，需要转换回原图坐标
        # 这里简化处理，实际应用中可能需要更复杂的坐标变换
        return actual_x, actual_y, actual_w, actual_h

    def convert_from_original_coordinates(self, x, y, w, h):
        """将原图坐标转换为显示坐标"""
        # 如果有旋转，先应用旋转变换
        # 这里简化处理
        actual_x, actual_y, actual_w, actual_h = x, y, w, h

        # 转换为显示坐标
        disp_x = actual_x * self.scale_factor
        disp_y = actual_y * self.scale_factor
        disp_w = actual_w * self.scale_factor
        disp_h = actual_h * self.scale_factor

        return disp_x, disp_y, disp_w, disp_h
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证SERIAL格式并改进识别算法
基于用户提供的正确答案：JUN23-1105
"""

import cv2
import numpy as np
import os
import json
import re
from datetime import datetime

def validate_serial_format(serial):
    """验证SERIAL格式"""
    patterns = [
        r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914, JUN23-1105
        r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
        r'^[A-Z]{3}\d{2}-\d{3,4}$',  # MAR23-1055
    ]
    
    for i, pattern in enumerate(patterns, 1):
        if re.match(pattern, serial):
            return True, f"模式{i}: {pattern}"
    
    return False, "不匹配任何已知模式"

def analyze_user_input():
    """分析用户提供的SERIAL"""
    user_serial = "JUN23-1105"
    
    print("🎯 用户识别结果验证")
    print("=" * 50)
    print(f"📝 用户识别的SERIAL: {user_serial}")
    
    is_valid, pattern_info = validate_serial_format(user_serial)
    
    if is_valid:
        print(f"✅ 格式验证: 有效")
        print(f"📋 匹配模式: {pattern_info}")
    else:
        print(f"❌ 格式验证: {pattern_info}")
    
    # 分析格式特征
    print(f"\n📊 格式分析:")
    print(f"   总长度: {len(user_serial)} 字符")
    print(f"   月份部分: {user_serial[:3]} (3个字母)")
    print(f"   年份部分: {user_serial[3:5]} (2个数字)")
    print(f"   分隔符: {user_serial[5]} (连字符)")
    print(f"   编号部分: {user_serial[6:]} (4个数字)")
    
    return user_serial

def update_known_serials(new_serial):
    """更新已知SERIAL列表"""
    # 加载现有标注
    try:
        with open("serial_number_annotations.json", 'r', encoding='utf-8') as f:
            annotations = json.load(f)
    except:
        annotations = {}
    
    # 提取所有已知SERIAL
    known_serials = []
    for filename, data_list in annotations.items():
        for data in data_list:
            if 'serial' in data:
                known_serials.append(data['serial'])
    
    # 添加新的SERIAL
    known_serials.append(new_serial)
    
    print(f"\n📚 更新后的已知SERIAL列表:")
    print("-" * 30)
    for i, serial in enumerate(known_serials, 1):
        print(f"{i:2d}. {serial}")
    
    # 分析格式分布
    format_count = {}
    for serial in known_serials:
        if re.match(r'^[A-Z]{3}\d{2}-\d{4}$', serial):
            format_count['月份年份-4位数字'] = format_count.get('月份年份-4位数字', 0) + 1
        elif re.match(r'^[A-Z]{2}\d{3}-\d{4}$', serial):
            format_count['2字母3数字-4位数字'] = format_count.get('2字母3数字-4位数字', 0) + 1
        elif re.match(r'^[A-Z]{3}\d{2}-\d{3}$', serial):
            format_count['月份年份-3位数字'] = format_count.get('月份年份-3位数字', 0) + 1
    
    print(f"\n📈 格式分布统计:")
    for format_type, count in format_count.items():
        print(f"   {format_type}: {count} 个")
    
    return known_serials

def create_improved_recognition_strategy():
    """基于新信息创建改进的识别策略"""
    print(f"\n🚀 改进识别策略建议:")
    print("=" * 50)
    
    strategies = [
        "1. 🎯 基于CLAHE+自适应阈值的方法",
        "   - 用户确认这种预处理效果最好",
        "   - 重点优化这个处理流程",
        "",
        "2. 🔍 区域定位优化", 
        "   - 先找到'SERIAL'文字",
        "   - 在其右侧固定距离查找数字",
        "   - 基于已知7个样本的位置特征",
        "",
        "3. 📏 尺寸和位置分析",
        "   - 分析7个已知SERIAL的位置规律",
        "   - 建立位置预测模型",
        "",
        "4. 🔤 字符识别优化",
        "   - 针对月份缩写(JAN,FEB,MAR,JUN,JUL,SEP等)",
        "   - 优化数字识别(23,1105等)",
        "",
        "5. 🎛️ 参数调优",
        "   - 基于成功案例调整OCR参数",
        "   - 优化图像预处理参数"
    ]
    
    for strategy in strategies:
        print(strategy)

def save_validation_result():
    """保存验证结果"""
    result = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "image": "微信图片_20250704123039.jpg",
        "user_identified_serial": "JUN23-1105",
        "validation": "VALID",
        "best_preprocessing": "CLAHE + 自适应阈值",
        "notes": "用户通过肉眼识别，确认预处理效果最佳的是CLAHE+自适应阈值方法"
    }
    
    with open("validation_result.json", 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 验证结果已保存到 validation_result.json")

def main():
    print("🎯 SERIAL识别验证和改进分析")
    print("=" * 60)
    
    # 验证用户输入
    user_serial = analyze_user_input()
    
    # 更新已知SERIAL列表
    known_serials = update_known_serials(user_serial)
    
    # 创建改进策略
    create_improved_recognition_strategy()
    
    # 保存结果
    save_validation_result()
    
    print(f"\n🎉 总结:")
    print(f"✅ 用户成功识别: JUN23-1105")
    print(f"✅ 最佳预处理方法: CLAHE + 自适应阈值")
    print(f"✅ 已知SERIAL总数: {len(known_serials)} 个")
    print(f"✅ 下一步: 基于这些信息优化自动识别算法")

if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
title SERIAL数字标注工具 - 专业版

echo ========================================
echo SERIAL数字标注工具 - 专业版
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python
    echo.
    echo 请先安装Python：
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载并安装最新版本的Python
    echo 3. 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo Python已安装，版本信息：
python --version
echo.

REM 检查dataset文件夹
if not exist "dataset" (
    echo 警告：未找到dataset文件夹
    echo 请确保dataset文件夹存在并包含要标注的图片
    echo.
    pause
    exit /b 1
)

echo 检查dataset文件夹...
set /a count=0
for %%f in (dataset\*.jpg dataset\*.jpeg dataset\*.png dataset\*.bmp) do (
    set /a count+=1
)

if %count%==0 (
    echo 警告：dataset文件夹中没有找到图片文件
    echo 支持的格式：jpg, jpeg, png, bmp
    echo.
    pause
    exit /b 1
) else (
    echo 找到 %count% 张图片待标注
)

echo.

REM 检查并安装必要的库
echo 正在检查必要的库...

python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo 错误：tkinter库未安装
    echo tkinter通常随Python一起安装，请检查Python安装
    pause
    exit /b 1
) else (
    echo tkinter库已安装
)

python -c "import cv2" >nul 2>&1
if errorlevel 1 (
    echo 正在安装opencv-python库...
    pip install opencv-python
    if errorlevel 1 (
        echo 错误：opencv-python库安装失败
        pause
        exit /b 1
    ) else (
        echo opencv-python库安装成功
    )
) else (
    echo opencv-python库已安装
)

python -c "import numpy" >nul 2>&1
if errorlevel 1 (
    echo 正在安装numpy库...
    pip install numpy
    if errorlevel 1 (
        echo 错误：numpy库安装失败
        pause
        exit /b 1
    ) else (
        echo numpy库安装成功
    )
) else (
    echo numpy库已安装
)

python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Pillow库...
    pip install Pillow
    if errorlevel 1 (
        echo 错误：Pillow库安装失败
        pause
        exit /b 1
    ) else (
        echo Pillow库安装成功
    )
) else (
    echo Pillow库已安装
)

echo.
echo 所有依赖库检查完成！
echo.
echo 功能说明：
echo 1. 专门用于标注SERIAL后面的数字
echo 2. 支持图片旋转功能
echo 3. 实时显示标注内容
echo 4. 自动保存标注数据
echo 5. 导出训练数据集
echo 6. 生成标注图片
echo.
echo 使用流程：
echo 1. 选择要标注的图片
echo 2. 如需要，旋转图片到合适角度
echo 3. 在图片上拖拽画框选中SERIAL数字
echo 4. 输入识别出的数字内容
echo 5. 点击"添加标注"保存
echo 6. 重复步骤3-5标注所有数字
echo 7. 完成后导出训练数据
echo.
echo 正在启动SERIAL数字标注工具...
echo.

REM 运行标注工具
if exist "serial_number_annotation_tool.py" (
    python serial_number_annotation_tool.py
) else (
    echo 错误：找不到 serial_number_annotation_tool.py 文件
    echo 请确保所有文件都在同一文件夹中
    pause
    exit /b 1
)

echo.
echo SERIAL数字标注工具已关闭
pause

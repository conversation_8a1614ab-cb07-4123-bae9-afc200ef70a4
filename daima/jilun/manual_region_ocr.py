#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动指定区域的SERIAL识别工具
您可以用鼠标选择SERIAL所在的区域，然后进行OCR识别
"""

import cv2
import numpy as np
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk
import pytesseract
import re
import json

class ManualRegionOCR:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("手动区域SERIAL识别")
        self.root.geometry("1200x800")
        
        self.current_image = None
        self.display_image = None
        self.current_image_path = ""
        self.scale_factor = 1.0
        
        # 鼠标选择相关
        self.selecting = False
        self.start_x = 0
        self.start_y = 0
        self.end_x = 0
        self.end_y = 0
        self.selection_rect = None
        
        self.setup_ui()
        self.load_test_images()
    
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding=10)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 图片选择
        ttk.Label(control_frame, text="选择图片:").pack(anchor=tk.W)
        self.image_var = tk.StringVar()
        self.image_combo = ttk.Combobox(control_frame, textvariable=self.image_var, 
                                       state="readonly", width=25)
        self.image_combo.pack(fill=tk.X, pady=(5, 10))
        self.image_combo.bind('<<ComboboxSelected>>', self.on_image_selected)
        
        # 或者选择文件
        ttk.Button(control_frame, text="📁 选择其他图片", 
                  command=self.select_file).pack(fill=tk.X, pady=(0, 10))
        
        # 操作说明
        instruction_frame = ttk.LabelFrame(control_frame, text="操作说明", padding=5)
        instruction_frame.pack(fill=tk.X, pady=(0, 10))
        
        instructions = [
            "1. 选择要识别的图片",
            "2. 在右侧图片上拖拽鼠标",
            "3. 框选SERIAL数字区域", 
            "4. 点击'识别选中区域'",
            "5. 查看识别结果"
        ]
        
        for instruction in instructions:
            ttk.Label(instruction_frame, text=instruction, font=("Arial", 9)).pack(anchor=tk.W)
        
        # OCR设置
        ocr_frame = ttk.LabelFrame(control_frame, text="OCR设置", padding=5)
        ocr_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 放大倍数
        ttk.Label(ocr_frame, text="放大倍数:").pack(anchor=tk.W)
        self.scale_var = tk.IntVar(value=3)
        scale_scale = ttk.Scale(ocr_frame, from_=1, to=5, orient=tk.HORIZONTAL,
                               variable=self.scale_var)
        scale_scale.pack(fill=tk.X)
        
        # 预处理方法
        ttk.Label(ocr_frame, text="预处理方法:").pack(anchor=tk.W, pady=(10, 0))
        self.preprocess_var = tk.StringVar(value="OTSU二值化")
        methods = ["原图", "OTSU二值化", "自适应阈值", "形态学处理"]
        preprocess_combo = ttk.Combobox(ocr_frame, textvariable=self.preprocess_var,
                                       values=methods, state="readonly")
        preprocess_combo.pack(fill=tk.X, pady=(5, 0))
        
        # 操作按钮
        ttk.Button(control_frame, text="🔍 识别选中区域", 
                  command=self.recognize_selection).pack(fill=tk.X, pady=10)
        
        ttk.Button(control_frame, text="🔄 清除选择", 
                  command=self.clear_selection).pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(control_frame, text="💾 保存结果", 
                  command=self.save_result).pack(fill=tk.X, pady=(0, 10))
        
        # 结果显示
        result_frame = ttk.LabelFrame(control_frame, text="识别结果", padding=5)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(result_frame, height=15, width=30, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 右侧图片显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图片显示 - 拖拽鼠标选择SERIAL区域", padding=5)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建Canvas用于显示图片和选择区域
        self.canvas = tk.Canvas(image_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
    
    def load_test_images(self):
        """加载测试图片"""
        ceshi_folder = "ceshi"
        if not os.path.exists(ceshi_folder):
            return
        
        image_files = []
        for file in os.listdir(ceshi_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_files.append(file)
        
        if image_files:
            self.image_combo['values'] = image_files
            self.image_combo.current(0)
            self.on_image_selected(None)
    
    def select_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[("图片文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.current_image_path = file_path
            self.load_current_image()
    
    def on_image_selected(self, event):
        """图片选择事件"""
        selected = self.image_var.get()
        if not selected:
            return
        
        self.current_image_path = os.path.join("ceshi", selected)
        self.load_current_image()
    
    def load_current_image(self):
        """加载当前图片"""
        try:
            # 使用cv2.imdecode处理中文路径
            with open(self.current_image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            self.current_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if self.current_image is None:
                messagebox.showerror("错误", "无法加载图片")
                return
            
            # 显示图片
            self.display_image_on_canvas()
            
            # 更新结果显示
            filename = os.path.basename(self.current_image_path)
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"📄 {filename}\n")
            self.result_text.insert(tk.END, f"📐 尺寸: {self.current_image.shape[1]}x{self.current_image.shape[0]}\n")
            self.result_text.insert(tk.END, "👆 请在图片上拖拽选择SERIAL区域\n\n")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载图片失败: {str(e)}")
    
    def display_image_on_canvas(self):
        """在Canvas上显示图片"""
        if self.current_image is None:
            return
        
        # 转换颜色空间
        rgb_image = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
        
        # 计算缩放比例以适应Canvas
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            # Canvas还没有正确初始化，延迟执行
            self.root.after(100, self.display_image_on_canvas)
            return
        
        img_height, img_width = rgb_image.shape[:2]
        
        scale_w = canvas_width / img_width
        scale_h = canvas_height / img_height
        self.scale_factor = min(scale_w, scale_h, 1.0)  # 不放大
        
        if self.scale_factor < 1.0:
            new_width = int(img_width * self.scale_factor)
            new_height = int(img_height * self.scale_factor)
            self.display_image = cv2.resize(rgb_image, (new_width, new_height), 
                                          interpolation=cv2.INTER_AREA)
        else:
            self.display_image = rgb_image
            self.scale_factor = 1.0
        
        # 转换为PIL格式并显示
        pil_image = Image.fromarray(self.display_image)
        self.photo = ImageTk.PhotoImage(pil_image)
        
        # 清除Canvas并显示图片
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        
        # 更新Canvas滚动区域
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def on_mouse_down(self, event):
        """鼠标按下事件"""
        self.selecting = True
        self.start_x = event.x
        self.start_y = event.y
        
        # 删除之前的选择框
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
    
    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.selecting:
            return
        
        self.end_x = event.x
        self.end_y = event.y
        
        # 删除之前的选择框
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
        
        # 绘制新的选择框
        self.selection_rect = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.end_x, self.end_y,
            outline="red", width=2
        )
    
    def on_mouse_up(self, event):
        """鼠标释放事件"""
        self.selecting = False
        self.end_x = event.x
        self.end_y = event.y
        
        # 计算选择区域
        x1 = min(self.start_x, self.end_x)
        y1 = min(self.start_y, self.end_y)
        x2 = max(self.start_x, self.end_x)
        y2 = max(self.start_y, self.end_y)
        
        width = x2 - x1
        height = y2 - y1
        
        if width > 10 and height > 10:
            self.result_text.insert(tk.END, f"✅ 已选择区域: ({x1},{y1}) {width}x{height}\n")
            self.result_text.insert(tk.END, "点击'识别选中区域'开始识别\n\n")
        else:
            self.result_text.insert(tk.END, "❌ 选择区域太小，请重新选择\n\n")
        
        self.result_text.see(tk.END)
    
    def recognize_selection(self):
        """识别选中区域"""
        if not self.selection_rect or self.current_image is None:
            messagebox.showwarning("警告", "请先选择图片和区域")
            return
        
        # 获取选择区域坐标（显示坐标）
        coords = self.canvas.coords(self.selection_rect)
        if len(coords) != 4:
            messagebox.showerror("错误", "无效的选择区域")
            return
        
        x1, y1, x2, y2 = coords
        
        # 转换为原图坐标
        orig_x1 = int(x1 / self.scale_factor)
        orig_y1 = int(y1 / self.scale_factor)
        orig_x2 = int(x2 / self.scale_factor)
        orig_y2 = int(y2 / self.scale_factor)
        
        # 确保坐标在图片范围内
        orig_x1 = max(0, min(orig_x1, self.current_image.shape[1]))
        orig_y1 = max(0, min(orig_y1, self.current_image.shape[0]))
        orig_x2 = max(0, min(orig_x2, self.current_image.shape[1]))
        orig_y2 = max(0, min(orig_y2, self.current_image.shape[0]))
        
        if orig_x2 <= orig_x1 or orig_y2 <= orig_y1:
            messagebox.showerror("错误", "无效的选择区域")
            return
        
        # 提取区域
        roi = self.current_image[orig_y1:orig_y2, orig_x1:orig_x2]
        
        self.result_text.insert(tk.END, f"🔍 正在识别区域: ({orig_x1},{orig_y1}) {orig_x2-orig_x1}x{orig_y2-orig_y1}\n")
        
        # 执行OCR
        results = self.perform_ocr(roi)
        
        # 显示结果
        self.result_text.insert(tk.END, f"📊 识别结果:\n")
        self.result_text.insert(tk.END, "-" * 30 + "\n")
        
        if results:
            for i, result in enumerate(results, 1):
                self.result_text.insert(tk.END, f"{i}. {result['text']}\n")
                self.result_text.insert(tk.END, f"   方法: {result['method']}\n")
                self.result_text.insert(tk.END, f"   有效: {'✅' if result['valid'] else '❓'}\n\n")
        else:
            self.result_text.insert(tk.END, "❌ 未识别到任何文字\n\n")
        
        self.result_text.see(tk.END)
    
    def perform_ocr(self, roi):
        """执行OCR识别"""
        if roi.size == 0:
            return []
        
        # 转换为灰度图
        if len(roi.shape) == 3:
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        else:
            gray = roi
        
        results = []
        
        # 根据选择的预处理方法处理图像
        preprocess_method = self.preprocess_var.get()
        
        if preprocess_method == "原图":
            processed = gray
        elif preprocess_method == "OTSU二值化":
            _, processed = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        elif preprocess_method == "自适应阈值":
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                            cv2.THRESH_BINARY, 11, 2)
        elif preprocess_method == "形态学处理":
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        else:
            processed = gray
        
        # 放大图像
        scale_factor = self.scale_var.get()
        enlarged = cv2.resize(processed, None, fx=scale_factor, fy=scale_factor, 
                            interpolation=cv2.INTER_CUBIC)
        
        try:
            # 多种OCR配置
            configs = [
                ('数字字母', '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-'),
                ('单行文字', '--oem 3 --psm 7'),
                ('单词', '--oem 3 --psm 8'),
                ('默认', '--oem 3 --psm 6'),
            ]
            
            for config_name, config in configs:
                text = pytesseract.image_to_string(enlarged, config=config).strip()
                clean_text = re.sub(r'[^A-Z0-9-]', '', text.upper())
                
                if clean_text:
                    is_valid = self.is_valid_serial_format(clean_text)
                    results.append({
                        'text': clean_text,
                        'method': f"{preprocess_method} + {config_name}",
                        'valid': is_valid,
                        'raw_text': text
                    })
        
        except Exception as e:
            self.result_text.insert(tk.END, f"❌ OCR失败: {str(e)}\n")
        
        return results
    
    def is_valid_serial_format(self, text):
        """验证SERIAL格式"""
        if not text or len(text) < 6:
            return False
        
        patterns = [
            r'^[A-Z]{3}\d{2}-\d{4}$',  # JAN23-7914
            r'^[A-Z]{2}\d{3}-\d{4}$',  # FE823-1036
            r'^[A-Z]{3}\d{2}-\d{3,4}$',  # MAR23-1055
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                return True
        
        return False
    
    def clear_selection(self):
        """清除选择"""
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
            self.selection_rect = None
        
        self.result_text.insert(tk.END, "🔄 已清除选择\n\n")
        self.result_text.see(tk.END)
    
    def save_result(self):
        """保存结果"""
        if not self.current_image_path:
            return
        
        filename = os.path.basename(self.current_image_path)
        result_text = self.result_text.get(1.0, tk.END)
        
        output_file = f"manual_ocr_result_{filename}.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result_text)
        
        messagebox.showinfo("成功", f"结果已保存到 {output_file}")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

def main():
    app = ManualRegionOCR()
    app.run()

if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
title SERIAL数字提取工具

echo ========================================
echo SERIAL数字提取工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未检测到Python，请先安装Python
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要的库是否安装
echo 正在检查必要的库...

python -c "import cv2" >nul 2>&1
if errorlevel 1 (
    echo 正在安装opencv-python库...
    pip install opencv-python
)

python -c "import numpy" >nul 2>&1
if errorlevel 1 (
    echo 正在安装numpy库...
    pip install numpy
)

python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 正在安装Pillow库...
    pip install Pillow
)

REM 尝试安装OCR库（可选）
python -c "import pytesseract" >nul 2>&1
if errorlevel 1 (
    echo 正在尝试安装pytesseract库（OCR功能）...
    pip install pytesseract
    if errorlevel 1 (
        echo 注意：OCR库安装失败，将使用简化版功能
    )
)

echo.
echo 库检查完成，正在启动程序...
echo.

REM 运行主程序
if exist "R1.py" (
    echo 启动完整版GUI工具...
    python R1.py
) else if exist "serial_extractor_simple.py" (
    echo 启动简化版工具...
    python serial_extractor_simple.py
) else (
    echo 错误：找不到程序文件
    echo 请确保R1.py或serial_extractor_simple.py文件存在
)

echo.
echo 程序执行完成
pause
